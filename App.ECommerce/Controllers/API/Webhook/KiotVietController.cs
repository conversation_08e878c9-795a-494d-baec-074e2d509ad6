using App.ECommerce.Models.Webhooks;
using Microsoft.AspNetCore.Mvc;
using App.ECommerce.Repository.Entities;
using Microsoft.Extensions.Localization;
using App.ECommerce.Units.Enums;
using App.ECommerce.Setting;
using App.ECommerce.Resource.Dtos.InputDtos;
using AutoMapper;
using App.ECommerce.Resource.Dtos.Webhooks;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Interface;
using Newtonsoft.Json.Linq;
using App.ECommerce.Resource.Dtos.KiotVietDtos;
using Newtonsoft.Json;
using App.ECommerce.Helpers.Interface;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Route(RoutePrefix.WEBHOOK)]
[ApiExplorerSettings(GroupName = "webhook-v1")]
public class KiotVietController : BaseController
{
    private readonly IMapper _mapper;
    private readonly IOrderFlow _orderFlow;
    private readonly ISyncServiceFlow _syncServiceFlow;
    private readonly IItemsRepository _itemsRepository;
    private readonly ICategoryRepository _categoryRepository;
    private readonly IConfiguration _configuration;
    private readonly IKiotVietHelper _kiotVietHelper;

    public KiotVietController(
        IStringLocalizer localizer,
        IMapper mapper,
        IOrderFlow orderFlow,
        ISyncServiceFlow syncServiceFlow,
        IItemsRepository itemsRepository,
        ICategoryRepository categoryRepository,
        IConfiguration configuration,
        IKiotVietHelper kiotVietHelper
    ) : base(localizer)
    {
        _mapper = mapper;
        _categoryRepository = categoryRepository;
        _orderFlow = orderFlow;
        _itemsRepository = itemsRepository;
        _syncServiceFlow = syncServiceFlow;
        _configuration = configuration;
        _kiotVietHelper = kiotVietHelper;
    }

    [HttpPost]
    public async Task<IActionResult> HandleKiotVietEvent([FromBody] KiotVietWebhookPayloadDto payloadDto)
    {
        try
        {

            // Collect all request information including headers
            var requestInfo = new
            {
                Method = Request.Method,
                Path = Request.Path.Value,
                QueryString = Request.QueryString.Value,
                Headers = Request.Headers.ToDictionary(h => h.Key, h => h.Value.ToArray()),
                ContentType = Request.ContentType,
                ContentLength = Request.ContentLength,
                UserAgent = Request.Headers["User-Agent"].FirstOrDefault(),
                RemoteIpAddress = HttpContext.Connection.RemoteIpAddress?.ToString(),
                Body = payloadDto
            };

            LogEvent(new EventLogDto
            {
                RefId = payloadDto?.Id ?? "",
                RefType = TypeFor.Other,
                Action = LogActionEnum.Webhook,
                Status = LogStatusEnum.Info,
                ActionAPI = $"{RoutePrefix.WEBHOOK}/kiotviet",
                Message = $"KiotViet Webhook Request Details",
                DataObject = requestInfo
            });

            if (payloadDto?.Notifications == null || !payloadDto.Notifications.Any())
            {
                LogEvent(new EventLogDto
                {
                    RefId = payloadDto?.Id ?? "",
                    RefType = TypeFor.Other,
                    Action = LogActionEnum.Webhook,
                    Status = LogStatusEnum.Warning,
                    ActionAPI = $"{RoutePrefix.WEBHOOK}/kiotviet",
                    Message = "No notifications in webhook payload",
                    DataObject = payloadDto
                });
                return Ok(WebhookResponseDto.CreateSuccessResponse());
            }

            var kiotVietConfig = await _syncServiceFlow.FindByDoMain("mystoretest1");
            if (kiotVietConfig == null)
            {
                LogEvent(new EventLogDto
                {
                    RefId = payloadDto.Id ?? "",
                    RefType = TypeFor.Other,
                    Action = LogActionEnum.Webhook,
                    Status = LogStatusEnum.Error,
                    ActionAPI = $"{RoutePrefix.WEBHOOK}/kiotviet",
                    Message = "KiotViet config not found",
                    DataObject = payloadDto
                });
                return Ok(WebhookResponseDto.CreateErrorResponse("CONFIG_NOT_FOUND", "KiotViet config not found"));
            }

            // Parse action để loại bỏ retailerId ở cuối
            var fullAction = payloadDto.Notifications.FirstOrDefault()?.Action; // "order.update.500899902"
            string cleanAction = null;

            if (!string.IsNullOrEmpty(fullAction))
            {
                var actionParts = fullAction.Split('.');
                if (actionParts.Length >= 3)
                {
                    cleanAction = string.Join(".", actionParts.Take(actionParts.Length - 1));
                }
                else
                {
                    cleanAction = fullAction;
                }
            }

            // Parse enum với action đã clean
            if (!Enum.TryParse<KiotVietWebhookEvent>(cleanAction?.Replace(".", "_"), true, out var eventType))
            {
                LogEvent(new EventLogDto
                {
                    RefId = payloadDto.Id ?? "",
                    RefType = TypeFor.Other,
                    Action = LogActionEnum.Webhook,
                    Status = LogStatusEnum.Error,
                    ActionAPI = $"{RoutePrefix.WEBHOOK}/kiotviet",
                    Message = $"Unknown event: {fullAction} (cleaned: {cleanAction})",
                    DataObject = payloadDto
                });
                return Ok(WebhookResponseDto.CreateErrorResponse("INVALID_EVENT", $"Unknown event: {fullAction}"));
            }

            string shopId = kiotVietConfig.ShopId;
            string accessToken = kiotVietConfig.AccessToken;

            switch (eventType)
            {
                case KiotVietWebhookEvent.product_update:
                    var productResult = await _syncServiceFlow.SyncProductFromWebhook(SyncServiceEnum.KiotViet, payloadDto.Notifications.FirstOrDefault()?.Data.FirstOrDefault(), shopId);
                    if (!productResult.IsSuccess)
                        return Ok(WebhookResponseDto.CreateErrorResponse("PRODUCT_ERROR", string.Join(", ", productResult.Errors)));
                    return Ok(WebhookResponseDto.CreateSuccessResponse());

                case KiotVietWebhookEvent.product_delete:
                    var productIds = ExtractProductIds(payloadDto.Notifications.FirstOrDefault()?.Data.FirstOrDefault());
                    if (productIds == null || productIds.Count == 0)
                        return Ok(WebhookResponseDto.CreateErrorResponse("INVALID_DATA", "Product IDs are invalid"));

                    var deleteProductResult = await _syncServiceFlow.DeleteProductsFromWebhook(SyncServiceEnum.KiotViet, productIds, shopId);
                    if (!deleteProductResult.IsSuccess)
                        return Ok(WebhookResponseDto.CreateErrorResponse("PRODUCT_DELETE_ERROR", string.Join(", ", deleteProductResult.Errors)));
                    return Ok(WebhookResponseDto.CreateSuccessResponse());

                case KiotVietWebhookEvent.stock_update:
                    var stockResult = await _kiotVietHelper.SyncKiotVietStockFromWebhook(payloadDto.Notifications.FirstOrDefault()?.Data.FirstOrDefault(), shopId);
                    if (!stockResult.IsSuccess)
                        return Ok(WebhookResponseDto.CreateErrorResponse("STOCK_ERROR", stockResult.Message));
                    return Ok(WebhookResponseDto.CreateSuccessResponse());

                case KiotVietWebhookEvent.order_update:
                case KiotVietWebhookEvent.invoice_update:
                    var orderResult = await _syncServiceFlow.SyncOrderFromWebhook(SyncServiceEnum.KiotViet, payloadDto.Notifications.FirstOrDefault()?.Data.FirstOrDefault(), shopId);
                    if (!orderResult.IsSuccess)
                        return Ok(WebhookResponseDto.CreateErrorResponse("ORDER_ERROR", string.Join(", ", orderResult.Errors)));
                    return Ok(WebhookResponseDto.CreateSuccessResponse());

                case KiotVietWebhookEvent.customer_update:
                    var customerResult = await _syncServiceFlow.SyncCustomerFromWebhook(SyncServiceEnum.KiotViet, payloadDto.Notifications.FirstOrDefault()?.Data, shopId);
                    if (!customerResult.IsSuccess)
                        return Ok(WebhookResponseDto.CreateErrorResponse("CUSTOMER_ERROR", string.Join(", ", customerResult.Errors)));
                    return Ok(WebhookResponseDto.CreateSuccessResponse());

                case KiotVietWebhookEvent.customer_delete:
                    var deleteCustomerResult = await _kiotVietHelper.DeleteKiotVietCustomerFromWebhook(payloadDto.Notifications.FirstOrDefault()?.Data.FirstOrDefault(), shopId);
                    if (!deleteCustomerResult.IsSuccess)
                        return Ok(WebhookResponseDto.CreateErrorResponse("CUSTOMER_DELETE_ERROR", deleteCustomerResult.Message));
                    return Ok(WebhookResponseDto.CreateSuccessResponse());

                case KiotVietWebhookEvent.category_update:
                    var categoryResult = await _kiotVietHelper.SyncKiotVietCategoryFromWebhook(payloadDto.Notifications.FirstOrDefault()?.Data.FirstOrDefault(), shopId);
                    if (!categoryResult.IsSuccess)
                        return Ok(WebhookResponseDto.CreateErrorResponse("CATEGORY_ERROR", categoryResult.Message));
                    return Ok(WebhookResponseDto.CreateSuccessResponse());

                case KiotVietWebhookEvent.category_delete:
                    var deleteCategoryResult = await _kiotVietHelper.DeleteKiotVietCategoryFromWebhook(payloadDto.Notifications.FirstOrDefault()?.Data.FirstOrDefault(), shopId);
                    if (!deleteCategoryResult.IsSuccess)
                        return Ok(WebhookResponseDto.CreateErrorResponse("CATEGORY_DELETE_ERROR", deleteCategoryResult.Message));
                    return Ok(WebhookResponseDto.CreateSuccessResponse());

                default:
                    LogEvent(new EventLogDto
                    {
                        RefId = payloadDto.Id ?? "",
                        RefType = TypeFor.Other,
                        Action = LogActionEnum.Webhook,
                        Status = LogStatusEnum.Warning,
                        ActionAPI = $"{RoutePrefix.WEBHOOK}/kiotviet",
                        Message = $"Unhandled event: {payloadDto.Attempt}",
                        DataObject = payloadDto
                    });
                    return Ok(WebhookResponseDto.CreateSuccessResponse());
            }
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Other,
                Action = LogActionEnum.Webhook,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.WEBHOOK}/kiotviet",
                Message = ex.Message,
                Exception = ex,
                DataObject = null
            });
            return Ok(WebhookResponseDto.CreateErrorResponse("FORBIDDEN", "Not Authenticated"));
        }
    }

    /// <summary>
    /// Extract product IDs from webhook data
    /// </summary>
    private List<int> ExtractProductIds(object data)
    {
        try
        {
            if (data is JArray jArray)
            {
                return jArray.ToObject<List<int>>();
            }

            if (data is JObject jObject)
            {
                var productId = jObject["Id"]?.Value<int>() ?? jObject["ProductId"]?.Value<int>();
                return productId.HasValue ? new List<int> { productId.Value } : new List<int>();
            }

            if (data is int singleId)
            {
                return new List<int> { singleId };
            }

            return new List<int>();
        }
        catch
        {
            return new List<int>();
        }
    }
}