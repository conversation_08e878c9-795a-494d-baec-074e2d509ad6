using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using App.ECommerce.Repository.Entities;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.KiotVietDtos;
using App.ECommerce.Units.Abstractions.Entities;

namespace App.ECommerce.Helpers.Interface
{
    public interface IKiotVietHelper
    {
        Task<Result<SyncServiceConfig>> SaveKiotVietConfig(SyncServiceConfigDto dto);
        Task<SyncServiceConfig> GetKiotVietConfig(string shopId);
        Task<Result<bool>> DeleteKiotVietConfig(string shopId);
        Task<Result<SyncServiceConfig>> UpdateKiotVietAccessCode(string shopId, string accessCode);
        Task<Result<bool>> SyncKiotVietProductFromWebhook(object productData, string shopId);
        Task<Result<bool>> SyncKiotVietOrderFromWebhook(object orderData, string shopId);
        Task<Result<bool>> SyncKiotVietCustomerFromWebhook(object customerData, string shopId);
        Task<Result<bool>> DeleteKiotVietProductsFromWebhook(object productIds);
        Task<Result<bool>> DeleteKiotVietOrderFromWebhook(object orderData, string shopId);
        Task<Result<bool>> UpdateOrderToKiotViet(Order order, string shopId);
        Task<Result<string>> GetAccessToken(string clientId, string clientSecret, string retailerId);
        Task<string> GetWebhookSecret(string shopId);
        bool VerifyWebhookSignature(string payload, string signature, string secret);
        Task<Result<bool>> SyncKiotVietStockFromWebhook(object stockData, string shopId);
        Task<Result<bool>> SyncKiotVietCategoryFromWebhook(object categoryData, string shopId);
        Task<Result<bool>> DeleteKiotVietCategoryFromWebhook(object categoryData, string shopId);
        Task<Result<bool>> DeleteKiotVietCustomerFromWebhook(object customerData, string shopId);
    }
}