{"MongoSettings": {"ConnectionString": "cg14eR7V9DSjrvRwW/vI+45QqdHOKmX8FsYd8PPyi1LI7+HnDUGI9y7/SEnH38NehgUYcb/yje6825QaXnPGT1qrGloEtwhZ", "Type": "ENJMXaQLY18=", "User": "Gbp7fWcu+4QyRNvvyH9Fiw==", "Pass": "U5mJWZmVkNuYJgSulkOkOrRwXF+EPWSi", "Host": "R0erK42UzRpZHQ94ftrY2A==", "Port": "bxZ8K6rxeII=", "Db": "86riOz6c7zLjfle4H1YmAg==", "Source": "VzOYV887RmvLaYjjsH0AwCrn36NmvsFlix+mv4kUHCO32abEZ/TgXSC2HQR9BoD/qwnMLF5UX+zEf9s6dRpDesCiqizksnV3Y/XlkDi9gr9zGhogbzhyONEi/N+Eo7+1DJsKxFSOa1Y="}, "ApplicationSettings": {"DomainWildcard": "dev-admin.evotech.vn", "KeySwagger": "kSKN/Q210VzwfiakBfhUUw==", "MobileClientId": "E7DX+VEvDM+iahazIoH943YWwOtdjVdn", "MobileClientSecret": "yD9IS//7lLHPBphGVTEmZBHD5ejCqUBo", "MobileClientCode": "NBu8hg24z/YrkWjoqdA5AA==", "MobileSecretKey": "aA1OU3FACNclcTc7LV43f9XWUtB41Mxw/+dubNTOcqXURsnSxLtkNg==", "MobileIVKey": "8V2HgL/gib57RwAm7+vHa7vU7Bykdsch", "WebClientId": "E7DX+VEvDM+iahazIoH943YWwOtdjVdn", "WebClientSecret": "yD9IS//7lLH54fmqx0lucWMk56jFNtxk", "WebClientCode": "Omgo+0lGSnLjTBBo4eUNXQ==", "WebSecretKey": "aA1OU3FACNclcTc7LV43f9XWUtB41Mxw/+dubNTOcqXURsnSxLtkNg==", "WebIVKey": "8V2HgL/gib57RwAm7+vHa7vU7Bykdsch", "DayExpiredHandshake": 30, "DayExpiredLogin": 30}, "TelegramOptions": {"DomainWebhook": "https://dev-admin.evotech.vn/api/sys/toolfortelegram", "SecretTokenWebhook": "SecretToken", "BotToken": "7900151659:AAEDijpZ3Gfk0t6xCybIFiQs2Was_1Ruf_w", "ChannelId": -1002396671716}, "S3Options": {"IsUseS3": true, "Domain": "https://dev-file.evotech.vn", "UseHttps": true, "Endpoint": "dev-file.evotech.vn", "BucketName": "evo-retail", "AccessKey": "EVarWQp0g0Vf12VwKAH7", "SecretKey": "LmQg7mEsgmAU5tEi6ktqlLrbcnuK1VQod6R3jQJa"}, "AllowedHosts": "*", "DetailedErrors": false, "Logging": {"LogLevel": {"Default": "Error", "Microsoft": "Error", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.AspNetCore.DataProtection": "Error", "Microsoft.AspNetCore.HttpsPolicy": "Error"}}, "Ahamove": {"BaseUrl": "https://partner-apistg.ahamove.com", "ApiKey": "+uxNx0IGYa2sl7UD+cdnAA74N7Xp26AdJ1Hg7SilXvDMZ8GFuIYiHUFiIX/TVCVm"}, "ServerIp": "*************", "Cloudflare": {"ApiToken": "****************************************", "ZoneId": "cd1cd9429a156774ab67b3757afb7485"}, "Zalo": {"APP_ID": "1609489340843478114", "SECRET_KEY": "e7AP3yQBwDX3dWGYq6o2", "URL_CALLBACK": "api/partner/zalooa<PERSON>/callback", "URL_API": "https://openapi.zalo.me", "URL_APP": "https://oauth.zaloapp.com", "URL_BUSINESS": "https://business.openapi.zalo.me", "URL_GRAPH": "https://graph.zalo.me/v2.0", "URL_ZMP": "https://zma.evotech.vn"}, "BaseDomain": "evotech.vn", "AES": {"Secret": "cM_MpqSNKkcDjwXWY4Wr"}, "Env": "Staging", "JTExpress": {"BaseUrl": "https://demoopenapi.jtexpress.vn/webopenplatformapi/api", "ApiAccount": "HrkLKlidWxXthdbvcpg7fTI6tIbyS3fS", "PrivateKey": "VM+LmbDMFonR/qQMFKTih/LYh0W8c5xZnxUeUWx4793tnG/n/uPBsw=="}, "ViettelInvoice": {"BaseUrl": "https://api-vinvoice.viettel.vn"}, "VietQR": "https://api.vietqr.io", "Promogame": {"DashboardUrl": "https://admin.play4promo.online", "ApiUrl": "https://api.play4promo.online/graphql", "DomainWebhook": "https://dev-admin.evotech.vn/api/webhook/promogame"}}