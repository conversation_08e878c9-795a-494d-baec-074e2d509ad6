using System.Collections.Generic;

using App.ECommerce.Repository.Entities;
using App.ECommerce.Units.Enums;

namespace App.ECommerce.Repository.Interface;

public interface ISyncServiceConfigRepository
{
    Task<SyncServiceConfig> CreateOrUpdate(SyncServiceConfig config);
    Task<SyncServiceConfig> FindByShopIdAndService(string shopId, SyncServiceEnum syncService);
    Task<SyncServiceConfig> UpdateAccessCode(string shopId, SyncServiceEnum syncService, string accessCode);
    Task<SyncServiceConfig> UpdateAccessToken(string shopId, SyncServiceEnum syncService, string accessToken);
    Task<SyncServiceConfig> UpdateBusinessId(string shopId, SyncServiceEnum syncService, int businessId);
    Task<SyncServiceConfig> UpdateVerifyToken(string shopId, SyncServiceEnum syncService, string verifyToken);
    Task<SyncServiceConfig> UpdateAdditionalConfig(string shopId, SyncServiceEnum syncService, string additionalConfig);
    Task<SyncServiceConfig> UpdateStatus(string shopId, SyncServiceEnum syncService, TypeStatus status);
    Task<SyncServiceConfig> FindByBusinessId(int businessId, SyncServiceEnum syncService);
    Task<bool> DeleteByShopIdAndService(string shopId, SyncServiceEnum syncService);
    Task<List<SyncServiceConfig>> GetAllConfigsByShopId(string shopId);
    Task<List<SyncServiceConfig>> GetAllConfigsByService(SyncServiceEnum syncService);
    Task<object> GetAccessTokenAsync(SyncServiceEnum syncService, string appId, string accessCode, string secretKey);

    Task<SyncServiceConfig> UpdateWebhookSecret(string shopId, SyncServiceEnum kiotViet, string encryptedSecret);
    Task<SyncServiceConfig> FindByAdditonal(string additional, SyncServiceEnum syncService);

}
