using System;
using System.Linq.Expressions;
using App.Base.Repository;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units.Enums;
using AutoMapper;
using log4net;
using MongoDB.Bson;
using MongoDB.Driver;

using Newtonsoft.Json;

using OfficeOpenXml;

namespace App.ECommerce.Repository.Implement;

public class UserGroupRepository : BaseRepository, IUserGroupRepository
{
    private readonly ILog _log = log4net.LogManager.GetLogger(typeof(UserGroupRepository));
    private readonly IMongoCollection<User> _collectionUser;
    private readonly IMongoCollection<UserGroup> _collectionUserGroup;
    private readonly IMongoCollection<UserGroupDetail> _collectionUserGroupDetail;
    private readonly IMongoCollection<Order> _collectionOrder;
    private readonly IMongoCollection<PointTransaction> _collectionPointTransaction;
    private readonly IMapper _mapper;

    public UserGroupRepository(IMapper mapper) : base()
    {
        _collectionUser = _database.GetCollection<User>($"User");
        _collectionUserGroup = _database.GetCollection<UserGroup>("UserGroup");
        _collectionUserGroupDetail = _database.GetCollection<UserGroupDetail>("UserGroupDetail");
        _collectionOrder = _database.GetCollection<Order>($"Order");
        _collectionPointTransaction = _database.GetCollection<PointTransaction>($"PointTransaction");
        _mapper = mapper;

        var indexOptions = new CreateIndexOptions();
        var indexModel = new List<CreateIndexModel<User>>
        {
            new CreateIndexModel<User>(Builders<User>.IndexKeys.Ascending(item => item.UserId), indexOptions),
            new CreateIndexModel<User>(Builders<User>.IndexKeys.Ascending(item => item.PhoneNumber), indexOptions),
            new CreateIndexModel<User>(Builders<User>.IndexKeys.Ascending(item => item.Email), indexOptions),
            new CreateIndexModel<User>(
                Builders<User>.IndexKeys
                    .Ascending(x => x.Status)
                    .Ascending(x => x.ShopId)
                    .Descending(x => x.Created),
                new CreateIndexOptions { Name = "status_shopid_created_index" })
        };
        _collectionUser.Indexes.CreateMany(indexModel);

        var indexOrderOptions = new CreateIndexOptions();
        var indexModelOrder = new List<CreateIndexModel<Order>>()
        {
            new CreateIndexModel<Order>(Builders<Order>.IndexKeys.Ascending(item => item.PartnerId), indexOrderOptions),
            new CreateIndexModel<Order>(Builders<Order>.IndexKeys.Ascending(item => item.ShopId), indexOrderOptions),
            new CreateIndexModel<Order>(Builders<Order>.IndexKeys.Ascending(item => item.OrderId), indexOrderOptions),
            new CreateIndexModel<Order>(Builders<Order>.IndexKeys.Ascending(item => item.TransactionId), indexOrderOptions),
        };
        _collectionOrder.Indexes.CreateMany(indexModelOrder);
    }

    #region Filter Base
    private FilterDefinition<User> BuildFilter(FilterConditionDto condition, FilterDefinitionBuilder<User> builder)
    {
        string field = condition.Field;

        switch (field)
        {
            case nameof(User.Fullname):
            case nameof(User.Email):
            case nameof(User.PhoneNumber):
                return BuildStringFilter(builder, field, condition);

            case nameof(User.Birthdate):
                return BuildDateFilter(builder, field, condition);

            case "Point":
                return BuildPointFilter(builder, field, condition);

            case nameof(User.IsZaloOA):
                return BuildBooleanFilter(builder, field, condition);

            case nameof(User.Status):
                return BuildEnumFilter<TypeStatus>(builder, field, condition);

            case nameof(User.Created):
                return BuildDateFilter(builder, field, condition);

            case "Age": // Tuổi
                return BuildAgeFilter(builder, condition);

            case "TotalOrderAmount": //Tổng tiền đơn hàng
                return BuildTotalOrderAmountFilter(builder, condition);

            default:
                return null;
        }
    }

    private FilterDefinition<User> BuildStringFilter(
        FilterDefinitionBuilder<User> builder,
        string field,
        FilterConditionDto condition)
    {
        var value = condition.Values.FirstOrDefault() ?? "";
        
        var escapedValue = System.Text.RegularExpressions.Regex.Escape(value.Trim());
        
        return condition.Operator switch
        {
            FilterOperatorEnum.Equals => builder.Eq(field, value),
            FilterOperatorEnum.NotEquals => builder.Ne(field, value),
            FilterOperatorEnum.Contains => builder.Regex(field, new BsonRegularExpression(escapedValue, "i")),
            FilterOperatorEnum.NotContains => builder.Not(builder.Regex(field, new BsonRegularExpression(escapedValue, "i"))),
            FilterOperatorEnum.StartsWith => builder.Regex(field, new BsonRegularExpression("^" + escapedValue, "i")),
            FilterOperatorEnum.EndsWith => builder.Regex(field, new BsonRegularExpression(escapedValue + "$", "i")),
            _ => null
        };
    }

    private FilterDefinition<User> BuildEnumFilter<TEnum>(
        FilterDefinitionBuilder<User> builder,
        string field,
        FilterConditionDto condition) where TEnum : struct
    {
        if (Enum.TryParse<TEnum>(condition.Values?.FirstOrDefault(), out var enumValue))
        {
            return builder.Eq(field, enumValue);
        }
        return null;
    }

    private FilterDefinition<User> BuildBooleanFilter(
        FilterDefinitionBuilder<User> builder,
        string field,
        FilterConditionDto condition)
    {
        if (bool.TryParse(condition.Values?.FirstOrDefault(), out bool value))
        {
            return builder.Eq(field, value);
        }
        return null;
    }

    private FilterDefinition<User> BuildNumberFilter(
        FilterDefinitionBuilder<User> builder,
        string field,
        FilterConditionDto condition)
    {
        if (int.TryParse(condition.Values?.FirstOrDefault(), out var val))
        {
            return condition.Operator switch
            {
                FilterOperatorEnum.Equals => builder.Eq(field, val),
                FilterOperatorEnum.NotEquals => builder.Ne(field, val),
                FilterOperatorEnum.GreaterThan => builder.Gt(field, val),
                FilterOperatorEnum.GreaterThanOrEqual => builder.Gte(field, val),
                FilterOperatorEnum.LessThan => builder.Lt(field, val),
                FilterOperatorEnum.LessThanOrEqual => builder.Lte(field, val),
                _ => null
            };
        }
        return null;
    }

    private FilterDefinition<User> BuildDecimalFilter(
        FilterDefinitionBuilder<User> builder,
        Expression<Func<User, decimal>> field,
        FilterConditionDto condition)
    {
        var val = Convert.ToDecimal(condition.Values.FirstOrDefault());

        return condition.Operator switch
        {
            FilterOperatorEnum.Equals => builder.Eq(field, val),
            FilterOperatorEnum.NotEquals => builder.Ne(field, val),
            FilterOperatorEnum.GreaterThan => builder.Gt(field, val),
            FilterOperatorEnum.GreaterThanOrEqual => builder.Gte(field, val),
            FilterOperatorEnum.LessThan => builder.Lt(field, val),
            FilterOperatorEnum.LessThanOrEqual => builder.Lte(field, val),
            _ => null
        };
    }

    private FilterDefinition<User> BuildDateFilter(
        FilterDefinitionBuilder<User> builder,
        string field,
        FilterConditionDto condition)
    {
        if (condition.Values == null || !condition.Values.Any())
            return null;

        if (condition.Operator == FilterOperatorEnum.Between && condition.Values.Count == 2)
        {
            if (DateTime.TryParse(condition.Values[0], out var from) &&
                DateTime.TryParse(condition.Values[1], out var to))
            {
                return builder.And(
                    builder.Gte(field, from.Date),
                    builder.Lt(field, to.Date.AddDays(1))
                );
            }
            return null;
        }

        if (DateTime.TryParse(condition.Values.First(), out var date))
        {
            return condition.Operator switch
            {
                FilterOperatorEnum.Equals => builder.Eq(field, date.Date),
                FilterOperatorEnum.NotEquals => builder.Ne(field, date.Date),
                FilterOperatorEnum.GreaterThan => builder.Gt(field, date.Date),
                FilterOperatorEnum.GreaterThanOrEqual => builder.Gte(field, date.Date),
                FilterOperatorEnum.LessThan => builder.Lt(field, date.Date),
                FilterOperatorEnum.LessThanOrEqual => builder.Lte(field, date.Date),
                _ => null
            };
        }
        return null;
    }

    private FilterDefinition<User> BuildPointFilter(
        FilterDefinitionBuilder<User> builder,
        string field,
        FilterConditionDto condition)
    {
        try
        {
            if (condition.Values == null || !condition.Values.Any())
                return null;

            if (condition.Operator == FilterOperatorEnum.Between && condition.Values.Count == 2)
            {
                var minPointStr = condition.Values[0].Replace(",", "");
                var maxPointStr = condition.Values[1].Replace(",", "");
                
                if (int.TryParse(minPointStr, out int minPoint) && 
                    int.TryParse(maxPointStr, out int maxPoint))
                {
                    var allUsers = _collectionUser.Find(builder.Empty).ToList();
                    var userIdsWithTargetPoint = new List<string>();

                    foreach (var user in allUsers)
                    {
                        var currentPoint = CalculateUserCurrentPoint(user);
                        
                        if (currentPoint >= minPoint && currentPoint <= maxPoint)
                        {
                            userIdsWithTargetPoint.Add(user.UserId);
                        }
                    }

                    return userIdsWithTargetPoint.Any() 
                        ? builder.In(x => x.UserId, userIdsWithTargetPoint)
                        : builder.Eq(x => x.UserId, "NO_MATCH");
                }
                return null;
            }

            var pointStr = condition.Values.First().Replace(",", "");
            int point = int.TryParse(pointStr, out int targetPoint) ? targetPoint : 0;

            if (point > 0)
            {
                var allUsers = _collectionUser.Find(builder.Empty).ToList();
                var userIdsWithTargetPoint = new List<string>();

                foreach (var user in allUsers)
                {
                    var currentPoint = CalculateUserCurrentPoint(user);
                    
                    bool shouldInclude = condition.Operator switch
                    {
                        FilterOperatorEnum.Equals => currentPoint == point,
                        FilterOperatorEnum.NotEquals => currentPoint != point,
                        FilterOperatorEnum.GreaterThan => currentPoint > point,
                        FilterOperatorEnum.GreaterThanOrEqual => currentPoint >= point,
                        FilterOperatorEnum.LessThan => currentPoint < point,
                        FilterOperatorEnum.LessThanOrEqual => currentPoint <= point,
                        _ => false
                    };

                    if (shouldInclude)
                    {
                        userIdsWithTargetPoint.Add(user.UserId);
                    }
                }

                return userIdsWithTargetPoint.Any() 
                    ? builder.In(x => x.UserId, userIdsWithTargetPoint)
                    : builder.Eq(x => x.UserId, "NO_MATCH");
            }
        }
        catch (Exception ex)
        {
            _log.Error($"BuildPointFilter | failed: {ex.Message}", ex);
        }

        return null;
    }

    /// <summary>
    /// Tính toán điểm hiện tại của user dựa trên logic từ CalculateUserScoreDetails
    /// </summary>
    private long CalculateUserCurrentPoint(User user)
    {
        try
        {
            // Tính tổng điểm tích lũy (IsAdditionEnabled = true)
            var sumPoints = _collectionPointTransaction
                .Find(t => t.UserId == user.UserId && t.IsAdditionEnabled == true)
                .ToList()
                .Sum(t => Math.Abs(t.PointsEarned));

            // Tính tổng điểm đã tiêu (IsAdditionEnabled = false)
            var spentPoints = _collectionPointTransaction
                .Find(t => t.UserId == user.UserId && t.IsAdditionEnabled == false)
                .ToList()
                .Sum(t => Math.Abs(t.PointsEarned));

            // Điểm hiện có = Tổng điểm tích lũy - Điểm đã tiêu
            return sumPoints - Math.Abs(spentPoints);
        }
        catch (Exception ex)
        {
            _log.Error($"CalculateUserCurrentPoint | failed for user {user.UserId}: {ex.Message}", ex);
            return 0;
        }
    }

    private FilterDefinition<User> BuildAgeFilter(
        FilterDefinitionBuilder<User> builder,
        FilterConditionDto condition)
    {
        try
        {
            if (condition.Values == null || !condition.Values.Any())
                return null;

            if (int.TryParse(condition.Values.First(), out int age))
            {
                var today = DateTime.Today;
                var birthdate = today.AddYears(-age);
                var nextBirthdate = birthdate.AddYears(1);

                return condition.Operator switch
                {
                    FilterOperatorEnum.Equals => builder.And(
                        builder.Gte(x => x.Birthdate, birthdate),
                        builder.Lt(x => x.Birthdate, nextBirthdate)
                    ),
                    FilterOperatorEnum.NotEquals => builder.Or(
                        builder.Lt(x => x.Birthdate, birthdate),
                        builder.Gte(x => x.Birthdate, nextBirthdate)
                    ),
                    FilterOperatorEnum.GreaterThan => builder.Lt(x => x.Birthdate, birthdate),
                    FilterOperatorEnum.GreaterThanOrEqual => builder.Lte(x => x.Birthdate, birthdate),
                    FilterOperatorEnum.LessThan => builder.Gte(x => x.Birthdate, nextBirthdate),
                    FilterOperatorEnum.LessThanOrEqual => builder.Gt(x => x.Birthdate, nextBirthdate),
                    _ => null
                };
            }
        }
        catch (Exception ex)
        {
            _log.Error($"BuildAgeFilter | failed: {ex.Message}", ex);
        }

        return null;
    }

    private FilterDefinition<User> BuildTotalOrderAmountFilter(
        FilterDefinitionBuilder<User> builder,
        FilterConditionDto condition)
    {
        try
        {
            if (condition.Values == null || !condition.Values.Any())
                return null;

            if (long.TryParse(condition.Values.First(), out long amount))
            {
                // Lấy danh sách UserId có tổng tiền đơn hàng thỏa mãn điều kiện
                var orderFilter = Builders<Order>.Filter.And(
                    Builders<Order>.Filter.Eq(x => x.StatusOrder, TypeOrderStatus.Success),
                    condition.Operator switch
                    {
                        FilterOperatorEnum.Equals => Builders<Order>.Filter.Eq(x => x.Price, amount),
                        FilterOperatorEnum.NotEquals => Builders<Order>.Filter.Ne(x => x.Price, amount),
                        FilterOperatorEnum.GreaterThan => Builders<Order>.Filter.Gt(x => x.Price, amount),
                        FilterOperatorEnum.GreaterThanOrEqual => Builders<Order>.Filter.Gte(x => x.Price, amount),
                        FilterOperatorEnum.LessThan => Builders<Order>.Filter.Lt(x => x.Price, amount),
                        FilterOperatorEnum.LessThanOrEqual => Builders<Order>.Filter.Lte(x => x.Price, amount),
                        _ => null
                    }
                );

                var orders = _collectionOrder.Find(orderFilter).ToList();
                var userIds = orders.Where(x => x.UserShippingAddress != null)
                                  .Select(x => x.UserShippingAddress.UserId)
                                  .Distinct()
                                  .ToList();

                return builder.In(x => x.UserId, userIds);
            }
        }
        catch (Exception ex)
        {
            _log.Error($"BuildTotalOrderAmountFilter | failed: {ex.Message}", ex);
        }

        return null;
    }
    #endregion

    public async Task<List<User>> FilterUser(FilterRequestDto request)
    {
        var builder = Builders<User>.Filter;
        var filters = new List<FilterDefinition<User>>();

        // Thêm filter mặc định theo shopId nếu có
        if (!string.IsNullOrEmpty(request.ShopId))
        {
            filters.Add(builder.Eq(x => x.ShopId, request.ShopId));
        }

        // Thêm các filter từ conditions
        if (request.Conditions != null && request.Conditions.Any())
        {
            var finalFilter = BuildFilter(request.Conditions[0], builder);
            if (finalFilter != null)
            {
                filters.Add(finalFilter);
            }

            for (int i = 1; i < request.Conditions.Count; i++)
            {
                var nextFilter = BuildFilter(request.Conditions[i], builder);
                if (nextFilter == null) continue;

                var logicOperator = request.Conditions[i - 1].LogicOperator ?? FilterLogicOperatorEnum.And;
                finalFilter = logicOperator == FilterLogicOperatorEnum.And
                    ? builder.And(finalFilter, nextFilter)
                    : builder.Or(finalFilter, nextFilter);

                filters.Add(finalFilter);
            }
        }

        // Kết hợp tất cả các filter
        var combinedFilter = filters.Any()
            ? builder.And(filters)
            : builder.Empty;

        var user = await _collectionUser.Find(combinedFilter).ToListAsync();

        if (!string.IsNullOrEmpty(request.GroupId))
        {
            var details = await _collectionUserGroupDetail.Find(x => x.GroupId == request.GroupId).ToListAsync();

            if (details != null)
            {
                // Lấy danh sách UserId từ details
                var existingUserIds = details.Select(x => x.UserId).ToList();

                // Lọc ra những user không tồn tại trong danh sách details
                user = user.Where(u => !existingUserIds.Contains(u.UserId)).ToList();
            }
        }

        return user;
    }

    public async Task<UserGroup> CreateAsync(UserGroup obj)
    {
        await _collectionUserGroup.InsertOneAsync(obj);

        return obj;
    }

    public async Task CreateGroupDetail(string groupId, UserGroupDetail obj)
    {
        await _collectionUserGroupDetail.InsertOneAsync(obj);
    }

    public async Task CreateGroupDetails(string groupId, List<UserGroupDetail> list)
    {
        var details = list.Select(d => new UserGroupDetail
        {
            GroupId = groupId,
            UserId = d.UserId
        });

        await _collectionUserGroupDetail.InsertManyAsync(details);
    }

    public async Task<bool> UpdateAsync(string id, UserGroup obj)
    {
        var filter = Builders<UserGroup>.Filter.Eq(x => x.GroupId, id);
        var update = Builders<UserGroup>.Update
            .Set(x => x.ShopId, obj.ShopId)
            .Set(x => x.GroupName, obj.GroupName)
            .Set(x => x.Description, obj.Description)
            .Set(x => x.Status, obj.Status)
            .Set(x => x.IsAuto, obj.IsAuto)
            .Set(x => x.Conditions, obj.Conditions);

        var result = await _collectionUserGroup.UpdateOneAsync(filter, update);

        return result.ModifiedCount > 0;
    }

    public async Task<bool> DeleteAsync(string shopId, string groupId)
    {
        try
        {
            var filter = Builders<UserGroup>.Filter.And(
                Builders<UserGroup>.Filter.Eq(x => x.ShopId, shopId),
                Builders<UserGroup>.Filter.Eq(x => x.GroupId, groupId)
            );

            var resultGroup = await _collectionUserGroup.DeleteOneAsync(filter);

            await DeleteDetailByGroupId(groupId);

            return resultGroup.DeletedCount > 0;
        }
        catch (Exception ex)
        {
            _log.Error(ex);

            return false;
        }
    }

    public async Task DeleteDetailByGroupId(string groupId)
    {
        await _collectionUserGroupDetail.DeleteManyAsync(x => x.GroupId == groupId);
    }

    public async Task<PagingResult<UserGroup>> GetListByShopIdAsync(UserGroupFilterDto obj)
    {
        var filter = Builders<UserGroup>.Filter.Empty;

        if (!string.IsNullOrEmpty(obj.ShopId))
            filter = filter & Builders<UserGroup>.Filter.Eq(x => x.ShopId, obj.ShopId);

        if (!string.IsNullOrEmpty(obj.GroupName))
        {
            var escapedGroupName = System.Text.RegularExpressions.Regex.Escape(obj.GroupName);
            filter = filter & Builders<UserGroup>.Filter.Regex(x => x.GroupName, new BsonRegularExpression(escapedGroupName, "i"));
        }

        var totalCount = await _collectionUserGroup.CountDocumentsAsync(filter);
        var sort = Builders<UserGroup>.Sort.Descending(x => x.CreatedDate);
        var options = new FindOptions<UserGroup>
        {
            Sort = sort,
            Skip = (obj.Paging.PageIndex * obj.Paging.PageSize),
            Limit = obj.Paging.PageSize
        };

        var list = await _collectionUserGroup.FindAsync(filter, options);
        var items = await list.ToListAsync();

        return new PagingResult<UserGroup>
        {
            Total = (int)totalCount,
            Result = items
        };
    }

    public async Task<UserGroupDto> GetUserGroupByGroupId(string shopId, string groupId)
    {
        try
        {
            var filter = Builders<UserGroup>.Filter.And(
                Builders<UserGroup>.Filter.Eq(x => x.ShopId, shopId),
                Builders<UserGroup>.Filter.Eq(x => x.GroupId, groupId)
            );
            var group = await _collectionUserGroup.Find(filter).FirstOrDefaultAsync();

            if (group != null)
            {
                var details = await _collectionUserGroupDetail.Find(x => x.GroupId == groupId).ToListAsync();

                var userIds = details.Select(x => x.UserId).ToList();

                var users = await _collectionUser.Find(x => userIds.Contains(x.UserId))
                    .Project(x => new { x.UserId, x.Fullname, x.Email, x.PhoneNumber })
                    .ToListAsync();

                var detailsWithUserInfo = details.Select(d =>
                {
                    var user = users.FirstOrDefault(u => u.UserId == d.UserId);
                    return new UserGroupDetailDto
                    {
                        GroupId = d.GroupId,
                        Source = d.Source,
                        UserId = d.UserId,
                        Fullname = user?.Fullname,
                        Email = user?.Email,
                        PhoneNumber = user?.PhoneNumber
                    };
                }).ToList();

                var result = _mapper.Map<UserGroupDto>(group);

                result.Details = detailsWithUserInfo;
                return result;
            }
        }
        catch (Exception ex)
        {
            _log.Error(ex);
        }

        return null;
    }

    public async Task<PagingResult<UserGroupDetailDto>> GetListUserByGroupByIdAsync(UserGroupDetailFilterDto obj)
    {
        try
        {
            var filter = Builders<UserGroup>.Filter.Empty;

            if (!string.IsNullOrEmpty(obj.ShopId))
                filter = filter & Builders<UserGroup>.Filter.Eq(x => x.ShopId, obj.ShopId);

            if (!string.IsNullOrEmpty(obj.GroupId))
                filter = filter & Builders<UserGroup>.Filter.Eq(x => x.GroupId, obj.GroupId);

            var group = await _collectionUserGroup.Find(filter).FirstOrDefaultAsync();

            if (group == null)
            {
                return new PagingResult<UserGroupDetailDto>
                {
                    Total = 0,
                    Result = new List<UserGroupDetailDto>()
                };
            }

            var details = await _collectionUserGroupDetail.Find(x => x.GroupId == obj.GroupId).ToListAsync();
            var userIds = details.Select(x => x.UserId).ToList();

            var userFilter = Builders<User>.Filter.In(x => x.UserId, userIds);

            var users = await _collectionUser.Find(userFilter)
                .Project(x => new { x.UserId, x.ReferralCode, x.Fullname, x.Email, x.PhoneNumber })
                .ToListAsync();

            if (!string.IsNullOrEmpty(obj.Paging.Search))
            {
                var searchTerm = obj.Paging.Search.Trim().ToLower();
                users = users.Where(u =>
                    (u.Fullname?.ToLower().Contains(searchTerm) ?? false) ||
                    (u.PhoneNumber?.ToLower().Contains(searchTerm) ?? false) ||
                    (u.Email?.ToLower().Contains(searchTerm) ?? false)
                ).ToList();
            }

            // Lọc details theo danh sách users đã tìm kiếm
            var userIdsAfterSearch = users.Select(u => u.UserId).ToList();
            details = details.Where(d => userIdsAfterSearch.Contains(d.UserId)).ToList();

            var detailsWithUserInfo = details
                .Select(d =>
                {
                    var user = users.FirstOrDefault(u => u.UserId == d.UserId);
                    return new UserGroupDetailDto
                    {
                        GroupId = d.GroupId,
                        Source = d.Source,
                        UserId = d.UserId,
                        ReferralCode = user?.ReferralCode,
                        Fullname = user?.Fullname,
                        Email = user?.Email,
                        PhoneNumber = user?.PhoneNumber
                    };
                })
                .ToList();

            // Áp dụng phân trang
            var pagedResults = detailsWithUserInfo
                .Skip(obj.Paging.PageIndex * obj.Paging.PageSize)
                .Take(obj.Paging.PageSize)
                .ToList();

            return new PagingResult<UserGroupDetailDto>
            {
                Total = detailsWithUserInfo.Count,
                Result = pagedResults
            };
        }
        catch (Exception ex)
        {
            _log.Error(ex);
            return new PagingResult<UserGroupDetailDto>
            {
                Total = 0,
                Result = new List<UserGroupDetailDto>()
            };
        }
    }

    public Task<byte[]> ExportUserGroupTemplate()
    {
        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add("Sheet1");
        var columns = ExportConst.HEADER_TEMPLATE_USER_GROUP;

        // Thêm header
        for (int i = 0; i < columns.Length; i++)
        {
            worksheet.Cells[1, i + 1].Value = columns[i];
            worksheet.Cells[1, i + 1].Style.Font.Bold = true;
        }

        // Data demo
        worksheet.Cells[2, 1].Value = "Nguyễn Văn A";
        worksheet.Cells[2, 2].Value = "<EMAIL>";
        worksheet.Cells[2, 3].Value = "0392345678";

        // Áp dụng style header
        using (var range = worksheet.Cells[1, 1, 1, columns.Length])
        {
            range.Style.Font.Bold = true;
            range.Style.Font.Name = "Times New Roman";
            range.Style.Font.Size = 13;
            range.Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            range.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        }

        // Áp dụng border + font cho toàn bộ bảng
        using (var range = worksheet.Cells[1, 1, 2, columns.Length])
        {
            range.Style.Font.Name = "Times New Roman";
            range.Style.Font.Size = 13;

            range.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
            range.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        }

        // Căn giữa các cột
        for (int i = 1; i <= columns.Length; i++)
        {
            worksheet.Cells[1, i, 2, i].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
        }

        for (int i = 1; i <= columns.Length; i++)
        {
            worksheet.Column(i).Width = 35;
        }

        return Task.FromResult(package.GetAsByteArray());
    }

    public Task<byte[]> ExportUserGroup(List<UserGroupDetailDto> list)
    {
        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add("Sheet1");

        // Định nghĩa các header
        var headers = ExportConst.HEADER_EXPORT_USER_GROUP;

        // Tạo header
        for (int i = 0; i < headers.Length; i++)
        {
            worksheet.Cells[1, i + 1].Value = headers[i];
        }

        // Áp dụng style cho header
        var headerRange = worksheet.Cells[1, 1, 1, headers.Length];
        headerRange.Style.Font.Bold = true;
        headerRange.Style.Font.Name = "Times New Roman";
        headerRange.Style.Font.Size = 13;
        headerRange.Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
        headerRange.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        headerRange.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        headerRange.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        headerRange.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;

        // Nếu không có dữ liệu, trả về file với chỉ header
        if (list == null || list.Count == 0)
        {
            for (int i = 1; i <= headers.Length; i++)
            {
                worksheet.Column(i).Width = 35;
            }

            return Task.FromResult(package.GetAsByteArray());
        }

        // Thêm dữ liệu thủ công
        for (int i = 0; i < list.Count; i++)
        {
            int row = i + 2; // Bắt đầu từ dòng 2 (sau header)

            worksheet.Cells[row, 1].Value = i + 1; // STT
            worksheet.Cells[row, 2].Value = list[i].ReferralCode ?? ""; // Mã khách hàng
            worksheet.Cells[row, 3].Value = list[i].Fullname ?? ""; // Tên khách hàng
            worksheet.Cells[row, 4].Value = list[i].PhoneNumber ?? ""; // Số điện thoại
            worksheet.Cells[row, 5].Value = list[i].Email ?? ""; // Email

        }

        // Áp dụng style cho toàn bộ bảng
        var dataRange = worksheet.Cells[1, 1, list.Count + 1, headers.Length];
        dataRange.Style.Font.Name = "Times New Roman";
        dataRange.Style.Font.Size = 13;
        dataRange.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        dataRange.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        dataRange.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
        dataRange.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;

        // Căn giữa các cột
        worksheet.Cells[2, 1, list.Count + 1, 1].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
        worksheet.Cells[2, 2, list.Count + 1, 2].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
        worksheet.Cells[2, 3, list.Count + 1, 3].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Left;
        worksheet.Cells[2, 4, list.Count + 1, 4].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
        worksheet.Cells[2, 5, list.Count + 1, 5].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Left;

        for (int i = 1; i <= headers.Length; i++)
        {
            worksheet.Column(i).Width = 35;
        }

        return Task.FromResult(package.GetAsByteArray());
    }

    public async Task<List<UserGroup>> GetUserGroupsByUserId(string userId)
    {
        var groupIds = await _collectionUserGroupDetail
            .Find(x => x.UserId == userId)
            .Project(x => x.GroupId)
            .ToListAsync();

        if (!groupIds.Any())
            return new List<UserGroup>();

        var filter = Builders<UserGroup>.Filter.In(x => x.GroupId, groupIds);
        return await _collectionUserGroup.Find(filter).ToListAsync();
    }
}
