using App.ECommerce.Models.Webhooks;
using Microsoft.AspNetCore.Mvc;
using App.ECommerce.Repository.Entities;
using Microsoft.Extensions.Localization;
using App.ECommerce.Units.Enums;
using App.ECommerce.Setting;
using App.ECommerce.Resource.Dtos.InputDtos;
using AutoMapper;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Interface;
using Newtonsoft.Json.Linq;
using App.ECommerce.Resource.Dtos.SapoOmniDtos;
using Newtonsoft.Json;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Route(RoutePrefix.WEBHOOK)]
[ApiExplorerSettings(GroupName = "webhook-v1")]
public class SapoController : BaseController
{
    private readonly IMapper _mapper;
    private readonly ISyncServiceConfigRepository _syncServiceConfigRepository;
    private readonly ISyncServiceFlow _syncServiceFlow;

    public SapoController(
        IStringLocalizer localizer,
        IMapper mapper,
        ISyncServiceConfigRepository syncServiceConfigRepository,
        ISyncServiceFlow syncServiceFlow
    ) : base(localizer)
    {
        _mapper = mapper;
        _syncServiceConfigRepository = syncServiceConfigRepository;
        _syncServiceFlow = syncServiceFlow;
    }

    [HttpPost]
    public async Task<IActionResult> HandleSapoOmniEvent([FromBody] object rawPayload)
    {
        string webhookId = Guid.NewGuid().ToString();
        string shopDomain = "";
        string topic = "";

        try
        {
            // Lấy thông tin từ SapoOmni headers
            shopDomain = Request.Headers["X-Sapo-Shop-Domain"].FirstOrDefault() ?? "";
            topic = Request.Headers["X-Sapo-Topic"].FirstOrDefault() ?? "";
            webhookId = Request.Headers["X-Sapo-Webhook-Id"].FirstOrDefault() ??
                       Request.Headers["X-Request-Id"].FirstOrDefault() ??
                       webhookId;

            var normalizedTopic = NormalizeTopic(topic);

            // Log webhook info
            LogEvent(new EventLogDto
            {
                RefId = webhookId,
                RefType = TypeFor.Other,
                Action = LogActionEnum.Webhook,
                Status = LogStatusEnum.Info,
                ActionAPI = $"{RoutePrefix.WEBHOOK}/sapo-omni",
                Message = $"SapoOmni webhook received - Topic: {topic}, Domain: {shopDomain}",
                DataObject = new
                {
                    Headers = new { TenantDomain = shopDomain, Topic = topic, WebhookId = webhookId },
                    PayloadType = rawPayload?.GetType()?.Name
                }
            });

            // Validate headers
            if (string.IsNullOrEmpty(shopDomain))
                return Ok(WebhookResponseDto.CreateErrorResponse("MISSING_DOMAIN", "X-Sapo-Tenant-Domain header is required"));

            if (string.IsNullOrEmpty(topic))
                return Ok(WebhookResponseDto.CreateErrorResponse("MISSING_TOPIC", "X-Sapo-Topic header is required"));

            // Tìm config
            var sapoOmniConfig = await _syncServiceConfigRepository.FindByAdditonal(shopDomain, SyncServiceEnum.Sapo);
            if (sapoOmniConfig == null)
            {
                LogEvent(new EventLogDto
                {
                    RefId = webhookId,
                    RefType = TypeFor.Other,
                    Action = LogActionEnum.Webhook,
                    Status = LogStatusEnum.Error,
                    ActionAPI = $"{RoutePrefix.WEBHOOK}",
                    Message = $"SapoOmni configuration not found for domain: {shopDomain}",
                    DataObject = new { RequestedDomain = shopDomain, Topic = topic }
                });
                return Ok(WebhookResponseDto.CreateErrorResponse("CONFIG_NOT_FOUND", $"Configuration not found for domain: {shopDomain}"));
            }

            // Parse topic
            if (!Enum.TryParse<SapoOmniWebhookTopic>(normalizedTopic, true, out var topicType))
            {
                LogEvent(new EventLogDto
                {
                    RefId = webhookId,
                    Status = LogStatusEnum.Error,
                    Message = $"Unknown topic: {topic}",
                    DataObject = new { OriginalTopic = topic, NormalizedTopic = normalizedTopic }
                });
                return Ok(WebhookResponseDto.CreateErrorResponse("INVALID_TOPIC", $"Unknown topic: {topic}"));
            }

            // Convert rawPayload to JToken for processing
            var jsonPayload = JToken.FromObject(rawPayload);

            // Xử lý từng loại webhook
            switch (topicType)
            {
                case SapoOmniWebhookTopic.products_create:
                case SapoOmniWebhookTopic.products_update:


                    var productResult = await _syncServiceFlow.SyncProductFromWebhook(SyncServiceEnum.Sapo, jsonPayload, sapoOmniConfig.ShopId);

                    return productResult.IsSuccess
                        ? Ok(WebhookResponseDto.CreateSuccessResponse())
                        : Ok(WebhookResponseDto.CreateErrorResponse("PRODUCT_ERROR", string.Join(", ", productResult.Errors)));

                case SapoOmniWebhookTopic.products_delete:
                    var productDeleteData = jsonPayload.ToObject<SapoOmniProductWebhookDto>();
                    if (productDeleteData == null || productDeleteData.Id == 0)
                    {
                        LogEvent(new EventLogDto
                        {
                            RefId = webhookId,
                            Status = LogStatusEnum.Error,
                            Message = "Product ID not found in delete payload",
                            DataObject = jsonPayload.ToString(Formatting.Indented)
                        });
                        return Ok(WebhookResponseDto.CreateErrorResponse("INVALID_DATA", "Product ID not found"));
                    }
                    var deleteResult = await _syncServiceFlow.DeleteProductsFromWebhook(SyncServiceEnum.Sapo, productDeleteData.Id.ToString(), sapoOmniConfig.ShopId);

                    return deleteResult.IsSuccess
                        ? Ok(WebhookResponseDto.CreateSuccessResponse())
                        : Ok(WebhookResponseDto.CreateErrorResponse("PRODUCT_DELETE_ERROR", string.Join(", ", deleteResult.Errors)));

                case SapoOmniWebhookTopic.orders_create:
                case SapoOmniWebhookTopic.orders_updated:
                    var orderData = jsonPayload.ToObject<SapoOmniOrderDto>();
                    if (orderData == null)
                    {
                        LogEvent(new EventLogDto
                        {
                            RefId = webhookId,
                            Status = LogStatusEnum.Error,
                            Message = "Failed to convert payload to SapoOmniOrderWebhookDto",
                            DataObject = jsonPayload.ToString(Formatting.Indented)
                        });
                        return Ok(WebhookResponseDto.CreateErrorResponse("INVALID_DATA", "Order data is invalid"));
                    }

                    var orderResult = await _syncServiceFlow.SyncOrderFromWebhook(SyncServiceEnum.Sapo, jsonPayload, sapoOmniConfig.ShopId);


                    return orderResult.IsSuccess
                        ? Ok(WebhookResponseDto.CreateSuccessResponse())
                        : Ok(WebhookResponseDto.CreateErrorResponse("ORDER_ERROR", string.Join(", ", orderResult.Errors)));

                case SapoOmniWebhookTopic.orders_cancelled:
                case SapoOmniWebhookTopic.orders_delete:


                    var orderDeleteResult = await _syncServiceFlow.DeleteOrderFromWebhook(SyncServiceEnum.Sapo, jsonPayload, sapoOmniConfig.ShopId);

                    return orderDeleteResult.IsSuccess
                        ? Ok(WebhookResponseDto.CreateSuccessResponse())
                        : Ok(WebhookResponseDto.CreateErrorResponse("ORDER_DELETE_ERROR", string.Join(", ", orderDeleteResult.Errors)));


                case SapoOmniWebhookTopic.customers_create:
                case SapoOmniWebhookTopic.customers_update:
                    var customerData = jsonPayload.ToObject<SapoOmniCustomerDto>();
                    if (customerData == null)
                    {
                        LogEvent(new EventLogDto
                        {
                            RefId = webhookId,
                            Status = LogStatusEnum.Error,
                            Message = "Failed to convert payload to SapoOmniCustomerWebhookDto",
                            DataObject = jsonPayload.ToString(Formatting.Indented)
                        });
                        return Ok(WebhookResponseDto.CreateErrorResponse("INVALID_DATA", "Customer data is invalid"));
                    }

                    var customerResult = await _syncServiceFlow.SyncCustomerFromWebhook(SyncServiceEnum.Sapo, customerData, sapoOmniConfig.ShopId
                       );

                    return customerResult.IsSuccess
                        ? Ok(WebhookResponseDto.CreateSuccessResponse())
                        : Ok(WebhookResponseDto.CreateErrorResponse("CUSTOMER_ERROR", string.Join(", ", customerResult.Errors)));

                default:
                    LogEvent(new EventLogDto
                    {
                        RefId = webhookId,
                        Status = LogStatusEnum.Warning,
                        Message = $"Unhandled webhook topic: {topicType}",
                        DataObject = new { Topic = topic, NormalizedTopic = normalizedTopic }
                    });
                    return Ok(WebhookResponseDto.CreateSuccessResponse());
            }
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = webhookId,
                RefType = TypeFor.Other,
                Action = LogActionEnum.Webhook,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.WEBHOOK}",
                Message = $"Webhook processing error: {ex.Message}",
                Exception = ex,
                DataObject = new
                {
                    Headers = new { TenantDomain = shopDomain, Topic = topic },
                    RawPayload = rawPayload
                }
            });
            return Ok(WebhookResponseDto.CreateErrorResponse("INTERNAL_ERROR", "Internal server error"));
        }
    }

    private string NormalizeTopic(string topic)
    {
        if (string.IsNullOrEmpty(topic)) return "";

        return topic
            .ToLower()
            .Replace("/", "_")
            .Replace("-", "_")
            .Replace(".", "_")
            .Replace(" ", "_");


    }
}