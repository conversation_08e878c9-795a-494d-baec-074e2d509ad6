using App.Base.Middleware;
using App.ECommerce.ProcessFlow;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Services.UploadStore;
using App.ECommerce.Setting;
using App.ECommerce.Units;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units.Enums;
using log4net;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using OfficeOpenXml;
using Action = App.ECommerce.Resource.Model.Action;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Produces("application/json")]
[Route(RoutePrefix.API_PARTNER)]
[ApiExplorerSettings(GroupName = "partner-v1")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
[MultiPolicysAuthorize(Policys = RolePrefix.Partner, Rules = "")]
public class UserGroupController : BaseController
{
    private readonly ILog _log = log4net.LogManager.GetLogger(typeof(UserPartnerController));
    private readonly IUserRepository _userRepository;
    private readonly IUserGroupRepository _userGroupRepository;
    private readonly IShopRepository _shopRepository;
    private readonly IPartnerRepository _partnerRepository;
    private readonly IGroupFileRepository _groupFileRepository;
    private readonly ICalcMemberLevelFlow _calcMemberLevelFlow;
    private readonly IStorageRepository _storageRepository;
    private readonly IFilterConditionRepository _filterConditionRepository;

    public UserGroupController(
        IStringLocalizer localizer,
        IUserRepository userRepository,
        IUserGroupRepository userGroupRepository,
        IShopRepository shopRepository,
        IPartnerRepository partnerRepository,
        IGroupFileRepository groupFileRepository,
        ICalcMemberLevelFlow calcMemberLevelFlow,
        IStorageRepository storageRepository,
        IFilterConditionRepository filterConditionRepository
    ) : base(localizer)
    {
        _userRepository = userRepository;
        _userGroupRepository = userGroupRepository;
        _shopRepository = shopRepository;
        _partnerRepository = partnerRepository;
        _groupFileRepository = groupFileRepository;
        _calcMemberLevelFlow = calcMemberLevelFlow;
        _storageRepository = storageRepository;
        _filterConditionRepository = filterConditionRepository;
    }

    /// <summary>
    /// Get List Filter Conditon (Danh sách lọc)
    /// </summary>
    /// <returns>Danh sách lọc</returns>
    [HttpGet("listfilter")]
    public async Task<IActionResult> GetListByShopId()
    {
        try
        {
            var groups = await _filterConditionRepository.GetAllAsync();
            return ResponseData(groups);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserGroup/listfilter",
                Message = $"Error GetListByShopId",
                Exception = ex,
                DataObject = null
            });

            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("INTERNAL_SERVER_ERROR"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Get List By Shop Id (Lấy danh sách nhóm người dùng theo shop ID)
    /// </summary>
    /// <param name="obj">Điều kiện tìm kiếm</param>
    /// <returns>Danh sách nhóm người dùng</returns>
    [HttpGet("getlist")]
    public async Task<IActionResult> GetListByShopId([FromQuery] UserGroupFilterDto obj)
    {
        try
        {
            var groups = await _userGroupRepository.GetListByShopIdAsync(obj);
            return ResponseData(groups);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserGroup/getlist",
                Message = $"Error GetListByShopId",
                Exception = ex,
                DataObject = obj
            });

            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("INTERNAL_SERVER_ERROR"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Get User Group By Id (Lấy thông tin nhóm người dùng theo ID)
    /// </summary>
    /// <param name="shopId">ID cửa hàng</param>
    /// <param name="groupId">ID của nhóm người dùng</param>
    /// <returns>Thông tin nhóm người dùng</returns>
    [HttpGet("{shopId}/{groupId}")]
    public async Task<IActionResult> GetById(string shopId, string groupId)
    {
        try
        {
            var group = await _userGroupRepository.GetUserGroupByGroupId(shopId, groupId);

            if (group == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("NOT_FOUND"), this.ControllerContext));

            return ResponseData(group);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserGroup/{shopId}/{groupId}",
                Message = $"Error GetById",
                Exception = ex,
                DataObject = null
            });

            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("INTERNAL_SERVER_ERROR"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Get List User Group By Id (Lấy danh sách người dùng theo ID nhóm người dùng)
    /// </summary>
    /// <param name="obj">Danh sách tham số tìm kiếm</param>
    /// <returns>Thông tin nhóm người dùng</returns>
    [HttpPost("user")]
    public async Task<IActionResult> GetListUserByGroupById([FromBody] UserGroupDetailFilterDto obj)
    {
        try
        {
            var group = await _userGroupRepository.GetListUserByGroupByIdAsync(obj);

            if (group == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("NOT_FOUND"), this.ControllerContext));

            return ResponseData(group);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserGroup/user",
                Message = $"Error GetById",
                Exception = ex,
                DataObject = obj
            });

            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("INTERNAL_SERVER_ERROR"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Create User Group (Tạo mới nhóm người dùng)
    /// </summary>
    /// <param name="obj">Thông tin nhóm người dùng</param>
    /// <returns>Nhóm người dùng đã tạo</returns>
    [HttpPost]
    public async Task<IActionResult> Create([FromBody] UserGroupDto obj)
    {
        try
        {
            var requestId = Guid.NewGuid().ToString();

            string partnerId = GetUserIdAuth();

            if (string.IsNullOrEmpty(partnerId))
                return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            var shop = _shopRepository.FindByShopId(obj.ShopId);
            if (shop == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));

            var partner = _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("PARTNER_NOT_FOUND"), this.ControllerContext));

            var objUserGroup = _mapper.Map<UserGroup>(obj);

            objUserGroup.PartnerId = partnerId;
            objUserGroup.Id = Guid.NewGuid();
            objUserGroup.GroupId = objUserGroup.Id.ToString();

            var result = await _userGroupRepository.CreateAsync(objUserGroup);

            if (result != null && obj.Details.Count > 0)
            {
                foreach (UserGroupDetailDto objUser in obj.Details) {
                    var objMap = _mapper.Map<UserGroupDetail>(objUser);

                    User user = _userRepository.FindByUserId(objUser.UserId);

                    if (user == null) {
                        user = new User
                        {
                            UserId = Guid.NewGuid().ToString(),
                            PartnerId = partnerId,
                            ShopId = obj.ShopId,
                            Fullname = objUser.Fullname,
                            Email = objUser.Email,
                            PhoneNumber = objUser.PhoneNumber
                        };

                        user = _userRepository.CreateUser(user);

                        await _calcMemberLevelFlow.CalcUserLevel(user.UserId);
                        await _calcMemberLevelFlow.CalcUserPointBySignUp(user.UserId);
                    }

                    objMap.GroupId = objUserGroup.GroupId;
                    objMap.UserId = user.UserId;

                    await _userGroupRepository.CreateGroupDetail(result.GroupId, objMap);
                }
            }

            LogEvent(new EventLogDto
            {
                RefId = partnerId,
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserGroup/Create",
                Message = $"Partner Created User Group",
                DataObject = objUserGroup
            });

            return ResponseData(new { Timestamp = DateTimes.Now(), Data = result, Message = localizer("SUCCESS") });
        }
        catch (Exception ex)
        {
            _log.Error(ex);

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserGroup/Create",
                Message = $"Error Create",
                Exception = ex,
                DataObject = null
            });

            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("INTERNAL_SERVER_ERROR"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Update User Group (Cập nhật nhóm người dùng)
    /// </summary>
    /// <param name="obj">Thông tin cập nhật</param>
    /// <returns>Kết quả cập nhật</returns>
    [HttpPut()]
    public async Task<IActionResult> Update([FromBody] UserGroupDto obj)
    {
        try
        {
            var requestId = Guid.NewGuid().ToString();

            string partnerId = GetUserIdAuth();

            if (string.IsNullOrEmpty(partnerId))
                return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            var existing = await _userGroupRepository.GetUserGroupByGroupId(obj.ShopId, obj.GroupId);
            if (existing == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("NOT_FOUND"), this.ControllerContext));

            var partner = _partnerRepository.FindByPartnerId(partnerId);

            if (partner == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("PARTNER_NOT_FOUND"), this.ControllerContext));

            var objUserGroup = _mapper.Map<UserGroup>(obj);

            objUserGroup.PartnerId = partnerId;

            var success = await _userGroupRepository.UpdateAsync(obj.GroupId, objUserGroup);

            if (!success)
                return ResponseData(new { Timestamp = DateTimes.Now(), Data = false, Message = localizer("FAIL") });
            else {
                await _userGroupRepository.DeleteDetailByGroupId(obj.GroupId);

                if (obj.Details.Count > 0) {
                    foreach (UserGroupDetailDto objUser in obj.Details) {
                        var objMap = _mapper.Map<UserGroupDetail>(objUser);

                        User user = _userRepository.FindByUserId(objUser.UserId);

                        if (user == null) {
                            user = new User
                            {
                                UserId = Guid.NewGuid().ToString(),
                                PartnerId = partnerId,
                                ShopId = obj.ShopId,
                                Fullname = objUser.Fullname,
                                Email = objUser.Email,
                                PhoneNumber = objUser.PhoneNumber
                            };

                            user = _userRepository.CreateUser(user);

                            await _calcMemberLevelFlow.CalcUserLevel(user.UserId);
                            await _calcMemberLevelFlow.CalcUserPointBySignUp(user.UserId);
                        }

                        objMap.UserId = user.UserId;
                        objMap.GroupId = obj.GroupId;

                        await _userGroupRepository.CreateGroupDetail(obj.GroupId, objMap);
                    }
                }
            }

            LogEvent(new EventLogDto
            {
                RefId = partnerId,
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserGroup/Update",
                Message = $"Partner Update User Group",
                DataObject = obj
            });

            return ResponseData(new { Timestamp = DateTimes.Now(), Data = success, Message = localizer("SUCCESS") });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserGroup/Update",
                Message = $"Partner Update User Group",
                DataObject = obj,
                Exception = ex
            });

            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("INTERNAL_SERVER_ERROR"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Delete User Group (Xóa nhóm người dùng)
    /// </summary>
    /// <param name="shopId">ID cửa hàng</param>
    /// <param name="groupId">ID của nhóm người dùng</param>
    /// <returns>Kết quả xóa</returns>
    [HttpDelete("{shopId}/{groupId}")]
    public async Task<IActionResult> Delete(string shopId, string groupId)
    {
        try
        {
            var requestId = Guid.NewGuid().ToString();

            string partnerId = GetUserIdAuth();

            if (string.IsNullOrEmpty(partnerId))
                return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            var existing = await _userGroupRepository.GetUserGroupByGroupId(shopId, groupId);
            if (existing == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("NOT_FOUND"), this.ControllerContext));

            var partner = _partnerRepository.FindByPartnerId(partnerId);

            if (partner == null)
                return ResponseBadRequest(new CustomBadRequest(localizer("PARTNER_NOT_FOUND"), this.ControllerContext));

            var success = await _userGroupRepository.DeleteAsync(shopId, groupId);
            if (!success)
                return ResponseBadRequest(new CustomBadRequest(localizer("USER_GROUP_DELETE_FAILED"), this.ControllerContext));

            return ResponseData(new { Timestamp = DateTimes.Now(), Data = success, Message = localizer("SUCCESS") });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Delete,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserPartner/Delete/{shopId}/{groupId}",
                Message = $"Error Delete",
                Exception = ex,
                DataObject = null
            });

            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("INTERNAL_SERVER_ERROR"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Filter User (Lọc danh sách người dùng)
    /// </summary>
    /// <param name="request">Điều kiện lọc</param>
    /// <returns>Danh sách người dùng</returns>
    [HttpPost("filter")]
    public async Task<IActionResult> Filter([FromBody] FilterRequestDto request)
    {
        try
        {
            var users = await _userGroupRepository.FilterUser(request);
            return ResponseData(users);
        }
        catch (Exception ex)
        {
            _log.Error($"Error Filter: {ex.Message}");

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserPartner/filter",
                Message = $"Error Filter",
                Exception = ex,
                DataObject = null
            });

            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("INTERNAL_SERVER_ERROR"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Export Excel template for user group import (Xuất file Excel mẫu để nhập dữ liệu người dùng)
    /// </summary>
    /// <returns>CSV file with template for user import</returns>
    // GET: api/partner/usergroup/export/template
    [HttpGet("export/template")]
    public async Task<IActionResult> ExportUserGroupTemplate()
    {
        try
        {
            var fileBytes = await _userGroupRepository.ExportUserGroupTemplate();
            var fileName = "TemplateUserGroup.xlsx";

            var file = File(fileBytes, ExportConst.EXCEL_CONTENT_TYPE, fileName);

            var link = S3Upload.SendMyFileToS3(fileBytes, ExportConst.EXCEL_CONTENT_TYPE, fileName, ExportConst.PATH_TEMPLATE).Result;

            if (!string.IsNullOrEmpty(link))
            {
                MediaFile objMedia = new MediaFile
                {
                    GroupFileId = "",
                    Type = TypeMedia.FILE,
                    Link = link,
                };

                objMedia = _groupFileRepository.CreateMediaFile(objMedia);
                MediaFileDto objMediaDto = _mapper.Map<MediaFileDto>(objMedia);

                return ResponseData(new
                {
                    Timestamp = DateTimes.Now(),
                    Data = objMediaDto,
                    Message = localizer("SUCCESS")
                });
            }
            else
            {
                return ResponseData(new
                {
                    Timestamp = DateTimes.Now(),
                    Data = new MediaFileDto(),
                    Message = localizer("FAIL")
                });
            }
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Export,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/api/partner/usergroup/export/template",
                Message = $"ExportUserGroupTemplate",
                Exception = ex,
                DataObject = null
            });

            return StatusCode(500, new
            {
                Timestamp = DateTime.Now,
                Message = localizer("INTERNAL_SERVER_ERROR"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Import users from Xlsx file (Nhập dữ liệu người dùng từ file Xlsx)
    /// </summary>
    /// <param name="file">Xlsx file containing user data</param>
    /// <param name="obj">ID of the shop</param>
    /// <returns>Result of the import process</returns>
    // POST: api/partner/UserPartner/ImportUsers
    [HttpPost("import")]
    public async Task<IActionResult> ImportUserGroup([FromForm] IFormFile file, [FromForm] ImportUserGroupDto obj)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null)
                return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            if (file == null || file.Length == 0)
                return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_TEMPLATE_FORMAT"), this.ControllerContext));

            if (!file.FileName.EndsWith(".xlsx"))
                return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_TEMPLATE_FORMAT"), this.ControllerContext));

            if (string.IsNullOrEmpty(obj.ShopId))
                return ResponseBadRequest(new CustomBadRequest(localizer("NOT_FOUND"), this.ControllerContext));

            var users = new List<User>();
            var errors = new List<string>();
            bool hasData = false;

            UserGroup objUserGroup = new UserGroup();
            List<UserGroupDetail> listUser = new List<UserGroupDetail>();
            using (var stream = new MemoryStream())
            {
                await file.CopyToAsync(stream);
                using (var package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets[0];

                    // Kiểm tra header
                    var headerRow = worksheet.Cells[1, 1, 1, ExportConst.HEADER_TEMPLATE_USER_GROUP.Length];
                    for (int i = 0; i < ExportConst.HEADER_TEMPLATE_USER_GROUP.Length; i++)
                    {
                        if (headerRow[1, i + 1].Text.Trim() != ExportConst.HEADER_TEMPLATE_USER_GROUP[i])
                        {
                            return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_TEMPLATE_FORMAT"), this.ControllerContext));
                        }
                    }

                    if (!string.IsNullOrEmpty(obj.GroupId)) {
                        var group = await _userGroupRepository.GetUserGroupByGroupId(obj.ShopId, obj.GroupId);

                        if (group != null) {
                            objUserGroup.GroupId = group.GroupId;
                            objUserGroup.GroupName = group.GroupName;
                            objUserGroup.Description = group.Description;
                        }
                    } 
                    else {
                        objUserGroup.PartnerId = partnerId;
                        objUserGroup.ShopId = obj.ShopId;
                        objUserGroup.Id = Guid.NewGuid();
                        objUserGroup.GroupId = objUserGroup.Id.ToString();
                        objUserGroup.GroupName = obj.GroupName;
                        objUserGroup.Description = obj.Description;
                        objUserGroup.IsAuto = false;
                        objUserGroup.Status = BaseStatusEnum.Active;
                    }

                    int rowCount = worksheet.Dimension.Rows;
                    for (int row = 2; row <= rowCount; row++)
                    {
                        try
                        {
                            string fullName = worksheet.Cells[row, 1].Text.Trim();
                            string email = worksheet.Cells[row, 2].Text.Trim();
                            string phoneNumber = worksheet.Cells[row, 3].Text.Trim();

                            // Validate dữ liệu bắt buộc
                            if (string.IsNullOrEmpty(fullName))
                            {
                                errors.Add($"{localizer("ROW")} {row}: {localizer("FULL_NAME")} {localizer("ROW_REQUIRED")}");
                                continue;
                            }

                            if (string.IsNullOrEmpty(phoneNumber))
                            {
                                errors.Add($"{localizer("ROW")} {row}: {localizer("PHONE_NUMBER")} {localizer("ROW_REQUIRED")}");
                                continue;
                            }

                            // Validate email nếu có
                            if (!string.IsNullOrEmpty(email) && !Common.IsValidEmail(email))
                            {
                                errors.Add($"{localizer("ROW")} {row}: {localizer("PHONE_NUMBER")} {localizer("ROW_INVALID")}");
                                continue;
                            }

                            // Validate số điện thoại
                            if (!Common.IsValidPhoneNumber(phoneNumber))
                            {
                                errors.Add($"{localizer("ROW")} {row}: {localizer("PHONE_NUMBER")} {localizer("ROW_INVALID")}");
                                continue;
                            }

                            // Kiểm tra số điện thoại trùng lặp
                            if (_userRepository.CheckPhoneNumber(obj.ShopId, phoneNumber.FormatPhoneToPlus84()))
                            {
                                errors.Add($"{localizer("ROW")} {row}: {localizer("PHONE_NUMBER")} {phoneNumber} {localizer("ROW_EXISTED")}");
                                continue;
                            }

                            var objUser = new User
                            {
                                PartnerId = partnerId,
                                ShopId = obj.ShopId,
                                UserId = Guid.NewGuid().ToString(),
                                Fullname = fullName,
                                ReferralCode = phoneNumber.FormatPhoneToZero(),
                                PhoneNumber = phoneNumber.FormatPhoneToPlus84(),
                                Email = email,
                                Status = TypeStatus.Actived
                            };

                            users.Add(objUser);

                            var objUserDetail = new UserGroupDetail
                            {
                                GroupId = objUserGroup.GroupId,
                                Source = BaseSourceEnum.Imported,
                                UserId = objUser.UserId
                            };

                            listUser.Add(objUserDetail);

                            hasData = true;
                        }
                        catch (Exception ex)
                        {
                            _log.Error($"Lỗi xử lý dòng {row}: {ex.Message}");
                        }
                    }
                }
            }

            if (errors.Any())
                return ResponseBadRequest(new CustomBadRequest(string.Join("\n", errors), this.ControllerContext));

            if (!hasData)
                return ResponseBadRequest(new CustomBadRequest(localizer("FILE_CONTAINS_NO_VALID_DATA"), this.ControllerContext));
            
            var reusultUser = await _userRepository.CreateUsers(users);
            
            foreach (var user in reusultUser)
            {
                await _calcMemberLevelFlow.CalcUserLevel(user.UserId);
                await _calcMemberLevelFlow.CalcUserPointBySignUp(user.UserId);
            }

            var result = await _userGroupRepository.CreateAsync(objUserGroup);

            if (result != null)
                await _userGroupRepository.CreateGroupDetails(result.GroupId, listUser);

            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("SUCCESS"),
                Data = result
            });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Import,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserPartner/ImportUsers",
                Message = $"Error ImportUsers",
                Exception = ex,
                DataObject = null
            });

            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("FAIL"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Export users to Xlsx file (Xuất dữ liệu người dùng sang file Xlsx)
    /// </summary>
    /// <param name="objFilter">Tham số tìm kiếm</param>
    /// <returns>Result of the import process</returns>
    // POST: api/partner/UserPartner/ImportUsers
    [HttpPost("export/user")]
    public async Task<IActionResult> ExportListUser([FromBody] UserGroupDetailFilterDto objFilter)
    {
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null)
                return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            var result = await _userGroupRepository.GetListUserByGroupByIdAsync(objFilter);

            if (result.Total > 0)
            {
                var fileBytes = await _userGroupRepository.ExportUserGroup(result.Result);
                var fileName = "ListUserGroupDetail.xlsx";

                UploadStorageDto obj = new UploadStorageDto
                {
                    FileBytes = fileBytes,
                    PrefixPath = ExportConst.PATH_IMPORT,
                    Type = TypeMedia.FILE,
                    FileName = fileName
                };

                MediaFile objMedia = await _storageRepository.UploadFileAsync(obj);

                if (objMedia != null)
                {
                    MediaFileDto objMediaDto = _mapper.Map<MediaFileDto>(objMedia);

                    return ResponseData(new
                    {
                        Timestamp = DateTimes.Now(),
                        Data = objMediaDto,
                        Message = localizer("SUCCESS")
                    });
                }
                else
                {
                    return ResponseData(new
                    {
                        Timestamp = DateTimes.Now(),
                        Data = new MediaFileDto(),
                        Message = localizer("FAIL")
                    });
                }
            }
            else
            {
                return ResponseData(new
                {
                    Timestamp = DateTimes.Now(),
                    Data = new MediaFileDto(),
                    Message = localizer("FAIL")
                });
            }
        }
        catch (Exception ex)
        {
            _log.Error(ex);

            LogExceptionEvent(_log, "ExportListUser", ex, null);

            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("INTERNAL_SERVER_ERROR"),
                Data = (object)null
            });
        }
    }
}