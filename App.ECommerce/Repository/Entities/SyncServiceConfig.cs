using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

using App.ECommerce.Units.Abstractions.Entities;
using App.ECommerce.Units.Enums;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace App.ECommerce.Repository.Entities;

public class SyncServiceConfig : EntityAuditBase<Guid>
{
    [Display(Name = "ShopId")]
    [BsonElement("ShopId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string ShopId { get; set; }

    [Display(Name = "SyncService")]
    [BsonElement("SyncService")]
    [BsonRepresentation(BsonType.String)]
    public SyncServiceEnum SyncService { get; set; }

    [Display(Name = "AppId")]
    [BsonElement("AppId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string AppId { get; set; }

    [Display(Name = "SecretKey")]
    [BsonElement("SecretKey")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string SecretKey { get; set; }

    [Display(Name = "AccessToken")]
    [BsonElement("AccessToken")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string AccessToken { get; set; }

    [Display(Name = "Status")]
    [BsonElement("Status")]
    [DefaultValue(TypeStatus.Actived)]
    public TypeStatus Status { get; set; } = TypeStatus.Actived;

    [Display(Name = "AccessCode")]
    [BsonElement("AccessCode")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string AccessCode { get; set; }

    [Display(Name = "BusinessId")]
    [BsonElement("BusinessId")]
    [BsonRepresentation(BsonType.Int32)]
    [DefaultValue("")]
    public int BusinessId { get; set; }

    [Display(Name = "VerifyToken")]
    [BsonElement("VerifyToken")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string VerifyToken { get; set; }

    /// <summary>
    /// Cấu hình bổ sung dạng JSON cho từng service cụ thể
    /// </summary>
    [Display(Name = "AdditionalConfig")]
    [BsonElement("AdditionalConfig")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string AdditionalConfig { get; set; }
    [Display(Name = "WebhookSecret")]
    [BsonElement("WebhookSecret")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string WebhookSecret { get; set; }
}