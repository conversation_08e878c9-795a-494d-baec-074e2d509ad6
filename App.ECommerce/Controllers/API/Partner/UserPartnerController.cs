using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text;
using App.Base.Middleware;
using App.Base.Utilities;
using App.ECommerce.ProcessFlow;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Setting;
using App.ECommerce.Units;
using log4net;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Action = App.ECommerce.Resource.Model.Action;
using App.ECommerce.Units.Consts;
using App.ECommerce.Services.UploadStore;
using OfficeOpenXml;
using System.Globalization;
using System.Threading.Tasks;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Units.Enums;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Helpers;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Produces("application/json")]
[Route(RoutePrefix.API_PARTNER)]
[ApiExplorerSettings(GroupName = "partner-v1")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
[MultiPolicysAuthorize(Policys = RolePrefix.Partner, Rules = "")]
public class UserPartnerController : BaseController
{
    private readonly ILog _log4net = log4net.LogManager.GetLogger(typeof(UserPartnerController));
    private readonly IUserRepository _userRepository;
    private readonly IPartnerRepository _partnerRepository;
    private readonly ITagRepository _tagRepository;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly IShopRepository _shopRepository;
    private readonly ICalcMemberLevelFlow _calcMemberLevelFlow;
    private readonly IMembershipLevelRepository _membershipLevelRepository;
    private readonly IGroupFileRepository _groupFileRepository;
    private readonly IStorageRepository _storageRepository;
    private readonly IAffiliationPartnerRepository _affiliationPartnerRepository;
    private readonly IUserFlow _userFlow;
    private readonly IOrderRepository _orderRepository;
    private readonly IBaseFlow _baseFlow;

    public UserPartnerController(
        IStringLocalizer localizer,
        IUserRepository userRepository,
        IPartnerRepository partnerRepository,
        ITagRepository tagRepository,
        IServiceScopeFactory serviceScopeFactory,
        IShopRepository shopRepository,
        ICalcMemberLevelFlow calcMemberLevelFlow,
        IMembershipLevelRepository membershipLevelRepository,
        IGroupFileRepository groupFileRepository,
        IStorageRepository storageRepository,
        IAffiliationPartnerRepository affiliationPartnerRepository,
        IUserFlow userFlow,
        IOrderRepository orderRepository,
        IBaseFlow baseFlow
    ) : base(localizer)
    {
        _userRepository = userRepository;
        _partnerRepository = partnerRepository;
        _tagRepository = tagRepository;
        _serviceScopeFactory = serviceScopeFactory;
        _shopRepository = shopRepository;
        _calcMemberLevelFlow = calcMemberLevelFlow;
        _membershipLevelRepository = membershipLevelRepository;
        _groupFileRepository = groupFileRepository;
        _storageRepository = storageRepository;
        _affiliationPartnerRepository = affiliationPartnerRepository;
        _userFlow = userFlow;
        _orderRepository = orderRepository;
        _baseFlow = baseFlow;
    }

    /// <summary>
    /// Partner create user (Đối tác tạo người dùng)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result CreateUser</returns>
    // POST: api/partner/UserPartner/CreateUser
    [HttpPost("CreateUser")]
    public async Task<IActionResult> CreateUserAsync(UserDto model)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), model.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));
            
            model.PhoneNumber = model.PhoneNumber.FormatPhonePrefix84();
            var validateResult = ValidateUniqueEmailAndPhone(model.Email, model.PhoneNumber, model.ShopId);
            if (validateResult != null) return validateResult; // Nếu có lỗi, trả về lỗi
            string randomPass = Guid.NewGuid().ToString("N").Substring(0, 10);

            var user = new User
            {
                UserId = Guid.NewGuid().ToString(),
                Fullname = model.Fullname,
                Email = model.Email,
                ShopId = model.ShopId,
                PhoneNumber = model.PhoneNumber,
                Password = randomPass.Encrypt(),
                PartnerId = baseDto.Partner.PartnerId
            };
            user = _userRepository.CreateUser(user);
            await _calcMemberLevelFlow.CalcUserLevel(user.UserId);
            await _calcMemberLevelFlow.CalcUserPointBySignUp(user.UserId);

            // Create tag associations
            if (model.Tags != null && model.Tags.Any())
            {
                _tagRepository.CreateTags("User", user.UserId, model.Tags, model.ShopId);
            }

            UserDto userDto = _mapper.Map<UserDto>(user);

            LogEvent(new EventLogDto
            {
                RefId = baseDto.Partner.PartnerId,
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserPartner/CreateUser",
                Message = $"Partner CreateUser Success",
                Exception = null,
                DataObject = null
            });

            return ResponseData(userDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserPartner/CreateUser",
                Message = $"Partner CreateUser Error",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.API_PARTNER}/User/CreateUser", ex);
        }
    }

    /// <summary>
    /// Get detail User (Lấy thông tin chi tiết người dùng)
    /// </summary>
    /// <param name="shopId"></param>
    /// <param name="userId"></param>
    /// <returns>The result UserDetail</returns>
    // GET: api/partner/UserPartner/UserDetail
    [HttpGet("UserDetail/{shopId}")]
    public async Task<IActionResult> GetUserAsync(string shopId, string userId)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));
            
            var user = _userRepository.FindByUserId(userId);
            if (user == null) return NotFound(new { message = "User not found" });
            UserDto userDto = _mapper.Map<UserDto>(user);
            // Find the user by userId
            // Find the tag targets associated with the user
            var tagTargets = _tagRepository.ListTagTarget("User", userId);

            // Get the tag IDs from the tag targets
            var tagIds = tagTargets.Select(target => target.TagId).ToList();

            // Find the tags using the tag IDs
            var tags = _tagRepository.ListTagNameByTagIds(tagIds);

            userDto.MembershipLevel = user.MembershipLevelId != null
                ? _membershipLevelRepository.FindByMembershipId(user.MembershipLevelId)
                : null;

            // Add the tags to the user object (assuming User class has a Tags property)
            userDto.Tags = tags;

            var transaction = _membershipLevelRepository.CalculateUserScoreDetails(user);

            userDto.Point = transaction.CurrentPoint;

            return ResponseData(userDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserPartner/UserDetail",
                Message = $"Error GetUser",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.API_PARTNER}/UserDetail", ex);
        }
    }

    /// <summary>
    /// Partner update user (Đối tác cập nhật thông tin người dùng)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result UpdateUse</returns>
    // PUT: api/partner/UserPartner/UpdateUser
    [HttpPut("UpdateUser")]
    public async Task<IActionResult> UpdateUserAsync(UserDto model)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), model.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            var user = _userRepository.FindByUserId(model.UserId);

            if (user == null) return ResponseBadRequest(new CustomBadRequest(localizer("BASE_USER_NOT_FOUND"), this.ControllerContext));

            if (user.ShopId != model.ShopId)
            {
                Shop? shop = _shopRepository.FindByShopId(model.ShopId);
                if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));

                if (shop.PartnerId != baseDto.Partner.PartnerId && shop.PartnerId != user.PartnerId)
                    return ResponseBadRequest(new CustomBadRequest(localizer("USER_NOT_PARTNER_MEMBER"), this.ControllerContext));
            }
            
            model.PhoneNumber = model.PhoneNumber.FormatPhonePrefix84();

            var validateResult = ValidateUniqueEmailAndPhone(model.Email, model.PhoneNumber, user.ShopId, user.UserId);

            if (validateResult != null) return validateResult;

            user.Fullname = model.Fullname;
            user.Email = model.Email;
            user.PhoneNumber = model.PhoneNumber;
            user.ReferralCode = model.PhoneNumber.FormatPhoneToZero();
            user.Birthdate = model.Birthdate != null ? model.Birthdate : null;
            user.Gender = model.Gender != null ? model.Gender : null;
            user.MembershipLevelId = model.MembershipLevelId != null ? model.MembershipLevelId : null;

            // Update other properties from model to user

            _userRepository.UpdateUser(user);

            if (model.Tags != null && model.Tags.Any())
            {
                _tagRepository.CreateTags("User", user.UserId, model.Tags, user.ShopId);
            }

            UserDto userDto = _mapper.Map<UserDto>(user);

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserPartner/UpdateUser",
                Message = $"Partner update User",
                Exception = null,
                DataObject = user
            });

            return ResponseData(userDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserPartner/UpdateUser",
                Message = $"Error UpdateUser",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.API_PARTNER}/UpdateUser", ex);
        }
    }

    /// <summary>
    /// Get list User of shop for Partner (Lấy danh sách người dùng theo cửa hảng của đối tác)
    /// </summary>
    /// <param name="model"></param>
    /// <param name="skip"></param>
    /// <param name="limit"></param>
    /// <returns>Result ListUser for Partner</returns>
    // GET: api/partner/UserPartner/ListUser
    [HttpGet("ListUser")]
    public async Task<IActionResult> ListUser([FromQuery] RequiredUserDto model, [FromQuery] int skip = 0, [FromQuery] int limit = 99)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), model.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            var result = await _userFlow.GetUserListAsync(baseDto.Partner.ParentId, model, skip, limit);

            return ResponseData(new
            {
                data = result.data,
                skip = result.skip,
                limit = result.limit,
                total = result.total
            });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserPartner/ListUser",
                Message = $"Error Partner get list User",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/User/ListUser", ex);
        }
    }

    /// <summary>
    /// Get list tag of user (Lấy danh sách tag của người dùng)
    /// </summary>
    /// <param name="model"></param>
    /// <param name="skip"></param>
    /// <param name="limit"></param>
    /// <returns>Result ListTag for Partner</returns>
    // GET: api/partner/UserPartner/ListTag
    [HttpGet("ListTag")]
    public async Task<IActionResult> ListUserTag([FromQuery] RequiredUserTagDto model, [FromQuery] int skip = 0, [FromQuery] int limit = 99)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), model.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            Paging paging = new Paging()
            {
                Search = $"{model.Search}",
                PageIndex = skip / (limit == 0 ? 1 : limit),
                PageSize = limit,
                NameType = TypeSortName.Created,
                SortType = TypeSort.desc
            };

            PagingResult<Tag> listUserTag = _tagRepository.ListTagByShop(paging, model.ShopId);
            List<Tag> listUserTagDto = _mapper.Map<List<Tag>>(listUserTag.Result);

            // Trả về kết quả
            return ResponseData(new
            {
                data = listUserTagDto,
                skip,
                limit,
                total = listUserTag.Total
            });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserPartner/ListTag",
                Message = $"Error Partner get list Tag",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/User/ListTag", ex);
        }
    }

    /// <summary>
    /// Delete users (Xóa nhiều người dùng)
    /// </summary>
    /// <param name="shopId"></param>
    /// <param name="userIds"></param>
    /// <returns>Result ListTag for Partner</returns>
    // POST: api/partner/UserPartner/ListTag
    [HttpDelete("DeleteUsers/{shopId}")]
    public async Task<IActionResult> DeleteUsers(string shopId, List<string> userIds)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            foreach (var userId in userIds)
            {
                var user = _userRepository.FindByUserId(userId);
                if (user == null)
                {
                    return ResponseUnauthorized(new CustomBadRequest(localizer("CART_USER_NOT_FOUND"), this.ControllerContext));
                }

                if (user != null)
                {
                    Shop? shop = _shopRepository.FindByShopId(user.ShopId);
                    if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
                    if (shop.PartnerId != baseDto.Partner.PartnerId) return ResponseBadRequest(new CustomBadRequest(localizer("USER_NOT_PARTNER_MEMBER"), this.ControllerContext));
                }
            }

            _userFlow.DeleteUsers(userIds);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = true, Message = localizer("AUTH_SUCCESS_DELETE") });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Delete,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserPartner/DeleteUsers",
                Message = $"Error DeleteUsers",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/User/DeleteUsers", ex);
        }
    }

    private IActionResult? ValidateUniqueEmailAndPhone(string email, string phoneNumber, string shopId, string? userId = null)
    {
        // Kiểm tra số điện thoại
        var phoneExisted = _userRepository.IsPhoneExistOnShop(phoneNumber.FormatPhoneToPlus84(), shopId, userId);
        if (phoneExisted) return ResponseBadRequest(new CustomBadRequest(localizer("AUTH_PHONE_REGISTERED_USER"), this.ControllerContext));

        if (string.IsNullOrWhiteSpace(email)) return null;

        // Kiểm tra email
        var emailExisted = _userRepository.IsEmailExistOnShop(email, shopId, userId);
        if (emailExisted) return ResponseBadRequest(new CustomBadRequest(localizer("AUTH_EMAIL_REGISTERED_USER"), this.ControllerContext));

        return null; // Không có lỗi
    }

    public class ListUserByUserIdsDto
    {
        [Required]
        [DefaultValue("")]
        public string ShopId { get; set; }

        [Required]
        public List<string> UserIds { get; set; }
    }

    /// <summary>
    /// Get list User by userIds (Lấy danh sách người dùng theo userIds)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Result ListUserByUserIds for Partner</returns>
    // POST: api/partner/UserPartner/ListUserByUserIds
    [HttpPost("ListUserByUserIds")]
    public async Task<IActionResult> ListUserByUserIds([FromBody] ListUserByUserIdsDto model)
    {
        try
        {
             var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), model.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            List<User> listUser = _userRepository.FindByUserIds(string.Join(",", model.UserIds));
            List<UserDto> listUserDto = _mapper.Map<List<UserDto>>(listUser);

            return ResponseData(new
            {
                data = listUserDto,
            });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserPartner/ListUserByUserIds",
                Message = $"Error Partner get list User by userIds",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/UserPartner/ListUserByUserIds", ex);
        }
    }

    public enum UpdateUserInfoAction
    {
        UpdateNotes,
        UpdateReferralCode,
        UpdateReferrerCode,
        UpdateTags,
        ChangePassword,

    }

    /// <summary>
    /// Update user info (Cập nhật thông tin khách hàng)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Return result of  UpdateUserInfo</returns>
    // PUT: api/partner/UserPartner/ListUserByUserIds
    [HttpPut("UpdateUserInfo")]
    public async Task<IActionResult> UpdateUserInfo([FromBody] UpdateUserInfoDto model)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), model.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            var user = _userRepository.FindByUserId(model.UserId);

            if (user == null) return ResponseBadRequest(new CustomBadRequest(localizer("BASE_USER_NOT_FOUND"), this.ControllerContext));
            if (model.UpdateAction == UpdateUserInfoAction.UpdateNotes)
            {
                user.Notes = model.Notes;
                _userRepository.UpdateUser(user);
            }

            if (model.UpdateAction == UpdateUserInfoAction.UpdateReferrerCode)
            {
                if (!string.IsNullOrWhiteSpace(model.ReferrerCode))
                {
                    User? referrer = _userRepository.FindByReferralCode(model.ReferrerCode, user.ShopId);
                    if (referrer == null || referrer.UserId == user.UserId) return ResponseBadRequest(new CustomBadRequest(localizer("REFERRER_CODE_INVALID"), this.ControllerContext));

                    // Thêm kiểm tra vòng lặp
                    if (_affiliationPartnerRepository.HasReferralCycle(user.UserId, referrer.UserId))
                    {
                        return ResponseBadRequest(new CustomBadRequest(localizer("REFERRER_CODE_INVALID_CYCLE"), this.ControllerContext));
                    }

                    user.ReferrerCode = model.ReferrerCode;
                    user.ParentId = referrer.UserId;
                }

                _userRepository.UpdateUser(user);

            }

            if (model.UpdateAction == UpdateUserInfoAction.ChangePassword)
            {

                if (model.UpdateAction == UpdateUserInfoAction.ChangePassword)
                {
                    var passwordErrors = PasswordValidator.Validate(model.Password, localizer);
                    if (passwordErrors.Count > 0)
                    {
                        return ResponseBadRequest(new CustomBadRequest(passwordErrors[0], this.ControllerContext));
                    }

                    user.Password = model.Password.Encrypt();
                   _userRepository.UpdateUser(user);
                }

            }

            if (model.UpdateAction == UpdateUserInfoAction.UpdateTags)
            {
                _tagRepository.CreateTags("User", user.UserId, model.Tags, user.ShopId);

            }

            UserDto userDto = _mapper.Map<UserDto>(user);

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserPartner/UpdateUserInfo",
                Message = $"Partner UpdateUserInfo",
                DataObject = user
            });

            return ResponseData(userDto);

        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserPartner/UpdateUserInfo",
                Message = $"Error Partner UpdateUserInfo",
                Exception = ex,
                DataObject = null
            });
            return LogExceptionEvent(_log4net, $"{RoutePrefix.API_PARTNER}/UserPartner/UpdateUserInfo", ex);
        }
    }

    /// <summary>
    /// Export Excel template for user import (Xuất file Excel mẫu để nhập dữ liệu người dùng)
    /// </summary>
    /// <param name="shopId"></param>
    /// <returns>CSV file with template for user import</returns>
    // GET: api/partner/UserPartner/ExportUserTemplate
    [HttpGet("ExportUserTemplate/{shopId}")]
    public async Task<IActionResult> ExportUserTemplate(string shopId)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            var fileBytes = await _userRepository.ExportUserTemplate();
            var fileName = "TemplateUser.xlsx";

            var file = File(fileBytes, ExportConst.EXCEL_CONTENT_TYPE, fileName);

            var link = S3Upload.SendMyFileToS3(fileBytes, ExportConst.EXCEL_CONTENT_TYPE, fileName, ExportConst.PATH_TEMPLATE).Result;

            _log4net.Info($"ExportUserTemplate link: {link}");

            if (!string.IsNullOrEmpty(link))
            {
                MediaFile objMedia = new MediaFile
                {
                    GroupFileId = "",
                    Type = TypeMedia.FILE,
                    Link = link,
                };

                objMedia = _groupFileRepository.CreateMediaFile(objMedia);
                MediaFileDto objMediaDto = _mapper.Map<MediaFileDto>(objMedia);

                return ResponseData(new
                {
                    Timestamp = DateTimes.Now(),
                    Data = objMediaDto,
                    Message = localizer("SUCCESS")
                });
            }
            else
            {
                return ResponseData(new
                {
                    Timestamp = DateTimes.Now(),
                    Data = new MediaFileDto(),
                    Message = localizer("FAIL")
                });
            }
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Export,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserPartner/ExportUserTemplate",
                Message = $"Error exporting user template",
                Exception = ex,
                DataObject = null
            });

            return StatusCode(500, new
            {
                Timestamp = DateTime.Now,
                Message = localizer("INTERNAL_SERVER_ERROR"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Import users from Xlsx file (Nhập dữ liệu người dùng từ file Xlsx)
    /// </summary>
    /// <param name="file">Xlsx file containing user data</param>
    /// <param name="shopId">ID of the shop</param>
    /// <returns>Result of the import process</returns>
    // POST: api/partner/UserPartner/ImportUsers
    [HttpPost("ImportUsers")]
    public async Task<IActionResult> ImportUsers([FromForm] IFormFile file, [FromForm] string shopId)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            if (file == null || file.Length == 0)
                return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_TEMPLATE_FORMAT"), this.ControllerContext));

            if (!file.FileName.EndsWith(".xlsx"))
                return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_TEMPLATE_FORMAT"), this.ControllerContext));

            if (string.IsNullOrEmpty(shopId))
                return ResponseBadRequest(new CustomBadRequest(localizer("NOT_FOUND"), this.ControllerContext));

            var users = new List<User>();
            var errors = new List<string>();
            bool hasData = false;

            using (var stream = new MemoryStream())
            {
                await file.CopyToAsync(stream);
                using (var package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets[0];

                    // Kiểm tra header
                    var headerRow = worksheet.Cells[1, 1, 1, ExportConst.HEADER_TEMPLATE_USER.Length];
                    for (int i = 0; i < ExportConst.HEADER_TEMPLATE_USER.Length; i++)
                    {
                        if (headerRow[1, i + 1].Text.Trim() != ExportConst.HEADER_TEMPLATE_USER[i])
                        {
                            return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_TEMPLATE_FORMAT"), this.ControllerContext));
                        }
                    }

                    int rowCount = worksheet.Dimension.Rows;
                    for (int row = 2; row <= rowCount; row++)
                    {
                        try
                        {
                            string fullName = worksheet.Cells[row, 1].Text.Trim();
                            string email = worksheet.Cells[row, 2].Text.Trim();
                            string phoneNumber = worksheet.Cells[row, 3].Text.Trim();
                            string genderText = worksheet.Cells[row, 4].Text.Trim();
                            string birthdateText = worksheet.Cells[row, 5].Text.Trim();

                            // Validate dữ liệu bắt buộc
                            if (string.IsNullOrEmpty(fullName))
                            {
                                errors.Add($"{localizer("ROW")} {row}: {localizer("FULL_NAME")} {localizer("ROW_REQUIRED")}");
                                continue;
                            }

                            if (string.IsNullOrEmpty(phoneNumber))
                            {
                                errors.Add($"{localizer("ROW")} {row}: {localizer("PHONE_NUMBER")} {localizer("ROW_REQUIRED")}");
                                continue;
                            }

                            // Validate email nếu có
                            if (!string.IsNullOrEmpty(email) && !Common.IsValidEmail(email))
                            {
                                errors.Add($"{localizer("ROW")} {row}: {localizer("PHONE_NUMBER")} {localizer("ROW_INVALID")}");
                                continue;
                            }

                            // Validate số điện thoại
                            if (!Common.IsValidPhoneNumber(phoneNumber))
                            {
                                errors.Add($"{localizer("ROW")} {row}: {localizer("PHONE_NUMBER")} {localizer("ROW_INVALID")}");
                                continue;
                            }

                            // Kiểm tra số điện thoại trùng lặp
                            if (_userRepository.CheckPhoneNumber(shopId, phoneNumber.FormatPhoneToPlus84()))
                            {
                                errors.Add($"{localizer("ROW")} {row}: {localizer("PHONE_NUMBER")} {phoneNumber} {localizer("ROW_EXISTED")}");
                                continue;
                            }

                            // Parse giới tính
                            TypeGender gender = TypeGender.Other;
                            if (!string.IsNullOrEmpty(genderText))
                            {
                                if (genderText.Equals("Nam", StringComparison.OrdinalIgnoreCase))
                                    gender = TypeGender.Male;
                                else if (genderText.Equals("Nữ", StringComparison.OrdinalIgnoreCase))
                                    gender = TypeGender.Female;
                            }

                            // Parse ngày sinh
                            DateTime? birthdate = null;
                            if (!string.IsNullOrEmpty(birthdateText))
                            {
                                string[] dateFormats = { "dd/MM/yyyy", "yyyy-MM-dd", "dd-MM-yyyy" };
                                foreach (string format in dateFormats)
                                {
                                    if (DateTime.TryParseExact(birthdateText, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime parsedDate))
                                    {
                                        birthdate = parsedDate;
                                        break;
                                    }
                                }
                            }

                            string randomPass = Guid.NewGuid().ToString("N").Substring(0, 10);
                            var user = new User
                            {
                                UserId = Guid.NewGuid().ToString(),
                                Fullname = fullName,
                                Email = email,
                                ReferralCode = phoneNumber.FormatPhoneToZero(),
                                PhoneNumber = phoneNumber.FormatPhoneToPlus84(),
                                Gender = gender,
                                Birthdate = birthdate,
                                ShopId = shopId,
                                Password = randomPass.Encrypt(),
                                PartnerId = baseDto.Partner.PartnerId
                            };

                            users.Add(user);
                            hasData = true;
                        }
                        catch (Exception ex)
                        {
                            _log4net.Error($"Lỗi xử lý dòng {row}: {ex.Message}");
                        }
                    }
                }
            }

            if (errors.Any())
            {
                return ResponseBadRequest(new CustomBadRequest(string.Join("\n", errors), this.ControllerContext));
            }


            if (!hasData)
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("FILE_CONTAINS_NO_VALID_DATA"), this.ControllerContext));
            }

            // Tạo users trong database
            var createdUsers = await _userRepository.CreateUsers(users);
            foreach (var user in createdUsers)
            {
                await _calcMemberLevelFlow.CalcUserLevel(user.UserId);
                await _calcMemberLevelFlow.CalcUserPointBySignUp(user.UserId);
            }

            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("SUCCESS"),
                Data = new { CreatedCount = createdUsers.Count }
            });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Import,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserPartner/ImportUsers",
                Message = $"Error ImportUsers",
                Exception = ex,
                DataObject = null
            });

            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("FAIL"),
                Data = (object)null
            });
        }
    }

    [HttpGet("exportuser")]
    public async Task<IActionResult> ExportListUser([FromQuery] RequiredUserDto model)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), model.ShopId);
            if (!baseDto.IsSuccess) return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));

            Paging paging = new Paging()
            {
                Search = $"{model.Search}",
                PageIndex = 0,
                PageSize = int.MaxValue,
                NameType = TypeSortName.Created,
                SortType = TypeSort.desc,
            };

            var (listUserDto, skip, limit, total) = await _userFlow.GetUserListAsync(baseDto.Partner.ParentId, model, 0, int.MaxValue);

            if (listUserDto.Count > 0)
            {
                var fileBytes = await _userRepository.ExportListUser(listUserDto);
                var fileName = "ListUser.xlsx";

                UploadStorageDto obj = new UploadStorageDto
                {
                    FileBytes = fileBytes,
                    PrefixPath = ExportConst.PATH_IMPORT,
                    Type = TypeMedia.FILE,
                    FileName = fileName
                };

                MediaFile objMedia = await _storageRepository.UploadFileAsync(obj);

                if (objMedia != null)
                {
                    MediaFileDto objMediaDto = _mapper.Map<MediaFileDto>(objMedia);

                    return ResponseData(new
                    {
                        Timestamp = DateTimes.Now(),
                        Data = objMediaDto,
                        Message = localizer("SUCCESS")
                    });
                }
                else
                {
                    return ResponseData(new
                    {
                        Timestamp = DateTimes.Now(),
                        Data = new MediaFileDto(),
                        Message = localizer("FAIL")
                    });
                }
            }
            else
            {
                return ResponseData(new
                {
                    Timestamp = DateTimes.Now(),
                    Data = new MediaFileDto(),
                    Message = localizer("FAIL")
                });
            }
        }
        catch (Exception ex)
        {
            _log4net.Error(ex);

            LogExceptionEvent(_log4net, "ExportListUser", ex, null);

            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("INTERNAL_SERVER_ERROR"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Reset lại hạng của người dùng (Reset level of user)
    /// </summary>
    /// <param name="shopId"></param>
    /// <param name="userId"></param>
    /// <returns>The result UserDetail</returns>
    // GET: api/partner/UserPartner/ResetRank
    [HttpGet("ResetRank/{shopId}/{userId}")]
    public async Task<IActionResult> ResetRank(string shopId, string userId)
    {
        try
        {
            var baseDto = await _baseFlow.ValidationPartner(GetUserIdAuth(), shopId);
            if (!baseDto.IsSuccess) 
                return ResponseUnauthorized(new CustomBadRequest(localizer(baseDto.ErrorMessage), this.ControllerContext));
            
            var user = _userRepository.FindByUserId(userId);
            if (user == null) 
                return ResponseBadRequest(new CustomBadRequest(localizer("USER_NOT_FOUND"), this.ControllerContext));
            
            await _calcMemberLevelFlow.CalcUserLevel(userId);

            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("SUCCESS"),
                Data = new { }
            });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/UserPartner/UserDetail",
                Message = $"Error GetUser",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.API_PARTNER}/UserDetail", ex);
        }
    }
}