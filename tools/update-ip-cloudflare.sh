#!/bin/bash

# ==== CẤU HÌNH ====
CF_API_TOKEN="LDUwW9XrxfXxhOz9h6kKo8C1-cRvF2ZoRvx7agcU"
ZONE_ID="cd1cd9429a156774ab67b3757afb7485"
OLD_IP="*************"
NEW_IP="**************"

# L<PERSON><PERSON> danh sách tất cả A records
RECORDS=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/dns_records?type=A&per_page=100" \
  -H "Authorization: Bearer $CF_API_TOKEN" \
  -H "Content-Type: application/json")

# Lặp qua từng record
echo "$RECORDS" | jq -c '.result[]' | while read -r record; do
  RECORD_ID=$(echo "$record" | jq -r '.id')
  NAME=$(echo "$record" | jq -r '.name')
  CONTENT=$(echo "$record" | jq -r '.content')

  if [[ "$CONTENT" == "$OLD_IP" ]]; then
    echo "🔁 Đang cập nhật record: $NAME ($OLD_IP → $NEW_IP)"
    # Gửi lệnh update
    curl -s -X PUT "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/dns_records/$RECORD_ID" \
      -H "Authorization: Bearer $CF_API_TOKEN" \
      -H "Content-Type: application/json" \
      --data '{
        "type": "A",
        "name": "'"$NAME"'",
        "content": "'"$NEW_IP"'",
        "ttl": 1,
        "proxied": false
      }' | jq .
  fi
done
