using App.ECommerce.Models.Webhooks;
using Microsoft.AspNetCore.Mvc;
using App.ECommerce.Repository.Entities;
using Microsoft.Extensions.Localization;
using App.ECommerce.Units.Enums;
using App.ECommerce.Setting;
using App.ECommerce.Resource.Dtos.InputDtos;
using AutoMapper;
using App.ECommerce.Resource.Dtos.Webhooks;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Interface;
using Newtonsoft.Json.Linq;
using App.ECommerce.Resource.Dtos.NhanhDtos;
using Newtonsoft.Json;


namespace App.ECommerce.Controllers.API;

[ApiController]
[Route(RoutePrefix.WEBHOOK)]
[ApiExplorerSettings(GroupName = "webhook-v1")]
public class NhanhController : BaseController
{
    private readonly IMapper _mapper;
    private readonly IOrderFlow _orderFlow;
    private readonly ISyncServiceFlow _syncServiceFlow;
    private readonly IItemsRepository _itemsRepository;
    private readonly ICategoryRepository _categoryRepository;
    private readonly IConfiguration _configuration;

    public NhanhController(
        IStringLocalizer localizer,
        IOrderFlow orderFlow,
        ISyncServiceFlow syncServiceFlow,
        IItemsRepository itemsRepository,
        ICategoryRepository categoryRepository,
        IConfiguration configuration
    ) : base(localizer)
    {
        _mapper = mapper;
        _categoryRepository = categoryRepository;
        _orderFlow = orderFlow;
        _itemsRepository = itemsRepository;
        _syncServiceFlow = syncServiceFlow;
        _configuration = configuration;
    }

    [HttpPost]
    public async Task<IActionResult> HandleNhanhEvent([FromBody] NhanhWebhookPayloadDto payloadDto)
    {
        try
        {
            LogEvent(new EventLogDto
            {
                RefId = payloadDto.BusinessId?.ToString() ?? "",
                RefType = TypeFor.Other,
                Action = LogActionEnum.Webhook,
                Status = LogStatusEnum.Info,
                ActionAPI = $"{RoutePrefix.WEBHOOK}/nhanh",
                Message = $"Request body from nhanh event: {payloadDto.Event}",
                DataObject = payloadDto
            });
            var nhanhconfig = await _syncServiceFlow.FindByBusinessId((int)payloadDto.BusinessId);
            string verifyToken = nhanhconfig != null ? nhanhconfig.VerifyToken : "";
            if (string.IsNullOrEmpty(verifyToken) || payloadDto.WebhooksVerifyToken != verifyToken)
            {
                LogEvent(new EventLogDto
                {
                    RefId = payloadDto.BusinessId?.ToString() ?? "",
                    RefType = TypeFor.Other,
                    Action = LogActionEnum.Webhook,
                    Status = LogStatusEnum.Error,
                    ActionAPI = $"{RoutePrefix.WEBHOOK}/nhanh",
                    Message = "Invalid webhook verify token",
                    DataObject = payloadDto
                });
                return Ok(WebhookResponseDto.CreateErrorResponse("INVALID_TOKEN", "Invalid webhook verify token"));
            }
            if (!Enum.TryParse<NhanhWebhookEvent>(payloadDto.Event, out var eventType))
            {
                LogEvent(new EventLogDto
                {
                    RefId = payloadDto.BusinessId?.ToString() ?? "",
                    RefType = TypeFor.Other,
                    Action = LogActionEnum.Webhook,
                    Status = LogStatusEnum.Error,
                    ActionAPI = $"{RoutePrefix.WEBHOOK}/nhanh",
                    Message = $"Unknown event: {payloadDto.Event}",
                    DataObject = payloadDto
                });
                return Ok(WebhookResponseDto.CreateErrorResponse("INVALID_EVENT", $"Unknown event: {payloadDto.Event}"));
            }
            switch (eventType)
            {
                case NhanhWebhookEvent.webhooksEnabled:
                    return Ok(WebhookResponseDto.CreateSuccessResponse());
                case NhanhWebhookEvent.productAdd:
                case NhanhWebhookEvent.productUpdate:
                    string shopId = nhanhconfig.ShopId;
                    string accessToken = nhanhconfig.AccessToken;
                    var rls = await _syncServiceFlow.SyncProductFromWebhook(SyncServiceEnum.NhanhVN, payloadDto.Data, shopId);
                    if (!rls.IsSuccess)
                        return Ok(WebhookResponseDto.CreateErrorResponse("PRODUCT_ERROR", string.Join(", ", rls.Errors)));
                    return Ok(WebhookResponseDto.CreateSuccessResponse());

                case NhanhWebhookEvent.productDelete:
                    var productIds = (payloadDto.Data as JArray)?.ToObject<List<int>>();
                    if (productIds == null || productIds.Count == 0)
                        return Ok(WebhookResponseDto.CreateErrorResponse("INVALID_DATA", "Data is invalid"));
                    var rlsDelete = await _syncServiceFlow.DeleteProductsFromWebhook(SyncServiceEnum.NhanhVN, productIds, nhanhconfig.ShopId);
                    if (!rlsDelete.IsSuccess)
                        return Ok(WebhookResponseDto.CreateErrorResponse("PRODUCT_DELETE_ERROR", string.Join(", ", rlsDelete.Errors)));
                    return Ok(WebhookResponseDto.CreateSuccessResponse());
                case NhanhWebhookEvent.inventoryChange:
                    var inventoryResult = await _syncServiceFlow.SyncInventoryFromWebhook(SyncServiceEnum.NhanhVN, payloadDto.Data, nhanhconfig.ShopId);
                    if (!inventoryResult.IsSuccess)
                        return Ok(WebhookResponseDto.CreateErrorResponse("INVENTORY_ERROR", string.Join(", ", inventoryResult.Errors)));
                    return Ok(WebhookResponseDto.CreateSuccessResponse());
                case NhanhWebhookEvent.orderAdd:
                case NhanhWebhookEvent.orderUpdate:
                    var result = await _syncServiceFlow.SyncOrderFromWebhook(SyncServiceEnum.NhanhVN, payloadDto.Data, nhanhconfig.ShopId);
                    if (!result.IsSuccess)
                        return Ok(WebhookResponseDto.CreateErrorResponse("ORDER_ERROR", string.Join(", ", result.Errors)));
                    return Ok(WebhookResponseDto.CreateSuccessResponse());

                case NhanhWebhookEvent.orderDelete:
                    var nhanhOrderDelete = (payloadDto.Data as JObject)?.ToObject<NhanhOrderDataDto>();
                    var resultDelete = await _syncServiceFlow.DeleteOrderFromWebhook(SyncServiceEnum.NhanhVN, nhanhOrderDelete, nhanhconfig.ShopId);
                    if (!resultDelete.IsSuccess)
                        return Ok(WebhookResponseDto.CreateErrorResponse("ORDER_DELETE_ERROR", string.Join(", ", resultDelete.Errors)));
                    return Ok(WebhookResponseDto.CreateSuccessResponse());

                case NhanhWebhookEvent.paymentReceived:
                    var paymentResult = await _syncServiceFlow.SyncOrderFromWebhook(SyncServiceEnum.NhanhVN, payloadDto.Data, nhanhconfig.ShopId);
                    if (!paymentResult.IsSuccess)
                        return Ok(WebhookResponseDto.CreateErrorResponse("PAYMENT_ERROR", string.Join(", ", paymentResult.Errors)));
                    return Ok(WebhookResponseDto.CreateSuccessResponse());
                default:
                    // Không xác định
                    break;
            }

            return Ok(WebhookResponseDto.CreateSuccessResponse());
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Other,
                Action = LogActionEnum.Webhook,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.WEBHOOK}/nhanh",
                Message = ex.Message,
                Exception = ex,
                DataObject = null
            });
            return Ok(WebhookResponseDto.CreateErrorResponse("FORBIDDEN", "Not Authenticated"));
        }
    }

}