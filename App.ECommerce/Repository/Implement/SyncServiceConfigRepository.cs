using System.Text;

using App.Base.Repository;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos.NhanhDtos;
using App.ECommerce.Units.Enums;

using MongoDB.Driver;

using Newtonsoft.Json;

namespace App.ECommerce.Repository.Implement;

public class SyncServiceConfigRepository : BaseRepository, ISyncServiceConfigRepository
{
    private readonly IMongoCollection<SyncServiceConfig> _collection;
    private readonly HttpClient _httpClient;
    private readonly string _nhanhVersion = "2.0";
    private readonly string _nhanhBaseUrl = "https://open.nhanh.vn/api";

    public SyncServiceConfigRepository(HttpClient httpClient)
    {
        _collection = _database.GetCollection<SyncServiceConfig>("SyncServiceConfig");
        _httpClient = httpClient;
    }

    public async Task<SyncServiceConfig> CreateOrUpdate(SyncServiceConfig config)
    {
        var existing = _collection.Find(x => x.ShopId == config.ShopId && x.SyncService == config.SyncService).FirstOrDefault();
        if (existing == null)
        {
            config.Id = Guid.NewGuid();
            config.CreatedDate = DateTime.Now;
            config.CreatedBy = config.CreatedBy;
            await _collection.InsertOneAsync(config);
        }
        else
        {
            var update = Builders<SyncServiceConfig>.Update
                .Set(x => x.AppId, config.AppId)
                .Set(x => x.SecretKey, config.SecretKey)
                .Set(x => x.AdditionalConfig, config.AdditionalConfig)
                .Set(x => x.ModifiedDate, DateTime.Now);
            await _collection.UpdateOneAsync(x => x.ShopId == config.ShopId && x.SyncService == config.SyncService, update);
        }
        return config;
    }

    public Task<SyncServiceConfig> FindByShopIdAndService(string shopId, SyncServiceEnum syncService)
    {
        return _collection.Find(x => x.ShopId == shopId && x.SyncService == syncService).FirstOrDefaultAsync();
    }

    public async Task<SyncServiceConfig> UpdateAccessCode(string shopId, SyncServiceEnum syncService, string accessCode)
    {
        var update = Builders<SyncServiceConfig>.Update
            .Set(x => x.AccessCode, accessCode)
            .Set(x => x.ModifiedDate, DateTime.Now);
        await _collection.UpdateOneAsync(x => x.ShopId == shopId && x.SyncService == syncService, update);
        return await _collection.Find(x => x.ShopId == shopId && x.SyncService == syncService).FirstOrDefaultAsync();
    }

    public async Task<SyncServiceConfig> UpdateAccessToken(string shopId, SyncServiceEnum syncService, string accessToken)
    {
        var update = Builders<SyncServiceConfig>.Update
            .Set(x => x.AccessToken, accessToken)
            .Set(x => x.ModifiedDate, DateTime.Now);
        await _collection.UpdateOneAsync(x => x.ShopId == shopId && x.SyncService == syncService, update);
        return await _collection.Find(x => x.ShopId == shopId && x.SyncService == syncService).FirstOrDefaultAsync();
    }

    public async Task<SyncServiceConfig> UpdateBusinessId(string shopId, SyncServiceEnum syncService, int businessId)
    {
        var update = Builders<SyncServiceConfig>.Update
            .Set(x => x.BusinessId, businessId)
            .Set(x => x.ModifiedDate, DateTime.Now);
        await _collection.UpdateOneAsync(x => x.ShopId == shopId && x.SyncService == syncService, update);
        return await _collection.Find(x => x.ShopId == shopId && x.SyncService == syncService).FirstOrDefaultAsync();
    }

    public async Task<SyncServiceConfig> UpdateVerifyToken(string shopId, SyncServiceEnum syncService, string verifyToken)
    {
        var update = Builders<SyncServiceConfig>.Update
            .Set(x => x.VerifyToken, verifyToken)
            .Set(x => x.ModifiedDate, DateTime.Now);
        await _collection.UpdateOneAsync(x => x.ShopId == shopId && x.SyncService == syncService, update);
        return await _collection.Find(x => x.ShopId == shopId && x.SyncService == syncService).FirstOrDefaultAsync();
    }

    public async Task<SyncServiceConfig> UpdateAdditionalConfig(string shopId, SyncServiceEnum syncService, string additionalConfig)
    {
        var update = Builders<SyncServiceConfig>.Update
            .Set(x => x.AdditionalConfig, additionalConfig)
            .Set(x => x.ModifiedDate, DateTime.Now);
        await _collection.UpdateOneAsync(x => x.ShopId == shopId && x.SyncService == syncService, update);
        return await _collection.Find(x => x.ShopId == shopId && x.SyncService == syncService).FirstOrDefaultAsync();
    }

    public async Task<SyncServiceConfig> UpdateStatus(string shopId, SyncServiceEnum syncService, TypeStatus status)
    {
        var update = Builders<SyncServiceConfig>.Update
            .Set(x => x.Status, status)
            .Set(x => x.ModifiedDate, DateTime.Now);
        await _collection.UpdateOneAsync(x => x.ShopId == shopId && x.SyncService == syncService, update);
        return await _collection.Find(x => x.ShopId == shopId && x.SyncService == syncService).FirstOrDefaultAsync();
    }

    public Task<SyncServiceConfig> FindByBusinessId(int businessId, SyncServiceEnum syncService)
    {
        return _collection.Find(x => x.BusinessId == businessId && x.SyncService == syncService).FirstOrDefaultAsync();
    }

    public async Task<bool> DeleteByShopIdAndService(string shopId, SyncServiceEnum syncService)
    {
        var result = await _collection.DeleteOneAsync(x => x.ShopId == shopId && x.SyncService == syncService);
        return result.DeletedCount > 0;
    }

    public async Task<List<SyncServiceConfig>> GetAllConfigsByShopId(string shopId)
    {
        return await _collection.Find(x => x.ShopId == shopId).ToListAsync();
    }

    public async Task<List<SyncServiceConfig>> GetAllConfigsByService(SyncServiceEnum syncService)
    {
        return await _collection.Find(x => x.SyncService == syncService).ToListAsync();
    }

    public Task<SyncServiceConfig> FindByAdditonal(string additional, SyncServiceEnum syncService)
    {
        return _collection.Find(x => x.AdditionalConfig == additional && x.SyncService == syncService).FirstOrDefaultAsync();
    }
    public IMongoCollection<SyncServiceConfig> GetCollection()
    {
        return _collection;
    }

    public async Task<object> GetAccessTokenAsync(SyncServiceEnum syncService, string appId, string accessCode, string secretKey)
    {
        return syncService switch
        {
            SyncServiceEnum.NhanhVN => await GetNhanhAccessTokenAsync(appId, accessCode, secretKey),
            SyncServiceEnum.Sapo => await GetSapoAccessTokenAsync(appId, accessCode, secretKey),
            SyncServiceEnum.KiotViet => await GetKiotVietAccessTokenAsync(appId, accessCode, secretKey),
            SyncServiceEnum.Odoo => await GetOdooAccessTokenAsync(appId, accessCode, secretKey),
            _ => throw new ArgumentException($"Không hỗ trợ sync service: {syncService}")
        };
    }
    public async Task<SyncServiceConfig> UpdateWebhookSecret(string shopId, SyncServiceEnum syncService, string encryptedSecret)
    {
        var update = Builders<SyncServiceConfig>.Update
            .Set(x => x.WebhookSecret, encryptedSecret)
            .Set(x => x.ModifiedDate, DateTime.Now);
        await _collection.UpdateOneAsync(x => x.ShopId == shopId && x.SyncService == syncService, update);
        return await _collection.Find(x => x.ShopId == shopId && x.SyncService == syncService).FirstOrDefaultAsync();
    }

    private async Task<NhanhAccessTokenResponseDto> GetNhanhAccessTokenAsync(string appId, string accessCode, string secretKey)
    {
        var url = $"{_nhanhBaseUrl}/oauth/access_token";
        var form = new MultipartFormDataContent
        {
            { new StringContent(_nhanhVersion), "version" },
            { new StringContent(appId), "appId" },
            { new StringContent(accessCode), "accessCode" },
            { new StringContent(secretKey), "secretKey" }
        };
        var response = await _httpClient.PostAsync(url, form);
        var responseString = await response.Content.ReadAsStringAsync();

        return JsonConvert.DeserializeObject<NhanhAccessTokenResponseDto>(responseString);
    }

    private async Task<object> GetSapoAccessTokenAsync(string appId, string accessCode, string secretKey)
    {
        throw new NotImplementedException("Sapo GetAccessToken chưa được implement");
    }

    private async Task<object> GetKiotVietAccessTokenAsync(string appId, string accessCode, string secretKey)
    {
        throw new NotImplementedException("KiotViet GetAccessToken chưa được implement");
    }

    private async Task<object> GetOdooAccessTokenAsync(string appId, string accessCode, string secretKey)
    {
        throw new NotImplementedException("Odoo GetAccessToken chưa được implement");
    }

}
