using App.Base.Repository.Entities;
using App.Base.Utilities;
using App.ECommerce.Helpers.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.KiotVietDtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Units.Abstractions.Entities;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units.Enums;
using App.ECommerce.ProcessFlow.Interface;
using AutoMapper;
using Newtonsoft.Json;
using System.Text;
using App.ECommerce.Services.UploadStore;
using System.Drawing;

namespace App.ECommerce.Helpers
{
    public class KiotVietHelper : IKiotVietHelper
    {
        protected readonly ICryptoRepository _cryptoRepository;
        protected readonly ISyncServiceConfigRepository _syncConfigRepository;
        protected readonly ISyncServiceHelper _syncServiceHelper;
        protected readonly IMapper _mapper;
        protected readonly IItemsRepository _itemsRepository;
        protected readonly IOrderRepository _orderRepository;
        protected readonly ICategoryRepository _categoryRepository;
        protected readonly IShopRepository _shopRepository;
        protected readonly IUserRepository _userRepository;
        protected readonly IWarehouseRepository _warehouseRepository;
        protected readonly IItemsFlow _itemsFlow;
        protected readonly IConfiguration _configuration;


        public KiotVietHelper(
            ICryptoRepository cryptoRepository,
            ISyncServiceConfigRepository syncConfigRepository,
            ISyncServiceHelper syncServiceHelper,
            IMapper mapper,
            IItemsRepository itemsRepository,
            IOrderRepository orderRepository,
            ICategoryRepository categoryRepository,
            IShopRepository shopRepository,
            IUserRepository userRepository,
            IWarehouseRepository warehouseRepository,
            IItemsFlow itemsFlow,
            IConfiguration configuration
        )
        {
            _cryptoRepository = cryptoRepository;
            _syncConfigRepository = syncConfigRepository;
            _syncServiceHelper = syncServiceHelper;
            _mapper = mapper;
            _itemsRepository = itemsRepository;
            _orderRepository = orderRepository;
            _categoryRepository = categoryRepository;
            _shopRepository = shopRepository;
            _userRepository = userRepository;
            _warehouseRepository = warehouseRepository;
            _itemsFlow = itemsFlow;
            _configuration = configuration;
        }

        public async Task<Result<SyncServiceConfig>> SaveKiotVietConfig(SyncServiceConfigDto dto)
        {
            try
            {
                // Mã hóa SecretKey trước khi lưu vào database
                string encryptedSecretKey = _cryptoRepository.Encrypt(dto.ClientSecret);

                var config = new SyncServiceConfig
                {
                    ShopId = dto.ShopId,
                    SyncService = SyncServiceEnum.KiotViet,
                    AppId = dto.ClientId,
                    SecretKey = encryptedSecretKey,
                    Status = TypeStatus.InActived,
                    AdditionalConfig = dto.DomainApi
                };

                // Lưu config vào database
                var savedConfig = await _syncConfigRepository.CreateOrUpdate(config);

                // Sau khi lưu config thành công, thực hiện lấy access token
                var tokenResult = await GetAccessTokenAfterSaveConfig(dto.ClientId, dto.ClientSecret, dto.DomainApi);

                if (tokenResult.IsSuccess)
                {
                    // Cập nhật access token vào config
                    await _syncConfigRepository.UpdateAccessToken(dto.ShopId, SyncServiceEnum.KiotViet, tokenResult.Data.access_token);

                    //// Cập nhật refresh token nếu có
                    //if (!string.IsNullOrEmpty(tokenResult.Data.refresh_token))
                    //{
                    //    await _syncConfigRepository.UpdateRefreshToken(dto.ShopId, SyncServiceEnum.KiotViet, tokenResult.Data.RefreshToken);
                    //}

                    // Tạo verify token nếu chưa có
                    if (string.IsNullOrEmpty(savedConfig.VerifyToken))
                    {
                        var verifyToken = TokenUtil.GenerateRandomVerifyToken();
                        await _syncConfigRepository.UpdateVerifyToken(dto.ShopId, SyncServiceEnum.KiotViet, verifyToken);
                        savedConfig.VerifyToken = verifyToken;
                    }

                    var webhookResult = await RegisterKiotVietWebhooks(tokenResult.Data.access_token, dto.ShopId, dto.DomainApi);

                    if (webhookResult.IsSuccess)
                    {
                        // Cập nhật trạng thái config thành actived
                        await _syncConfigRepository.UpdateStatus(dto.ShopId, SyncServiceEnum.KiotViet, TypeStatus.Actived);
                        savedConfig.Status = TypeStatus.Actived;
                        savedConfig.AccessToken = tokenResult.Data.access_token;

                        return Result<SyncServiceConfig>.Success(savedConfig);
                    }
                    else
                    {
                        // Nếu đăng ký webhook thất bại, vẫn lưu config nhưng với warning
                        await _syncConfigRepository.UpdateStatus(dto.ShopId, SyncServiceEnum.KiotViet, TypeStatus.Actived);
                        savedConfig.Status = TypeStatus.Actived;
                        savedConfig.AccessToken = tokenResult.Data.access_token;

                        return Result<SyncServiceConfig>.Success(savedConfig, $"Config saved but webhook registration failed: {webhookResult.Message}");
                    }
                }
                else
                {
                    // Nếu không lấy được token, giữ config với status InActived
                    return Result<SyncServiceConfig>.Failure($"SYNC_KIOTVIET_GET_TOKEN_ERROR: {tokenResult.Message}");
                }
            }
            catch (Exception ex)
            {
                return Result<SyncServiceConfig>.Failure($"SYNC_KIOTVIET_CONFIG_SAVE_ERROR: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy access token từ KiotViet API sau khi lưu config
        /// </summary>
        private async Task<Result<KiotVietTokenResponseDto>> GetAccessTokenAfterSaveConfig(string clientId, string clientSecret, string domain)
        {
            try
            {
                using var httpClient = new HttpClient();
                var tokenUrl = "https://id.kiotviet.vn/connect/token";

                // Theo tài liệu KiotViet API - sử dụng application/x-www-form-urlencoded
                var requestParams = new List<KeyValuePair<string, string>>
                {
                    new("scopes", "PublicApi.Access"),
                    new("grant_type", "client_credentials"),
                    new("client_id", clientId),
                    new("client_secret", clientSecret)
                };

                var content = new FormUrlEncodedContent(requestParams);

                var response = await httpClient.PostAsync(tokenUrl, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var tokenResponse = JsonConvert.DeserializeObject<KiotVietTokenResponseDto>(responseContent);

                    // Validate token response
                    if (string.IsNullOrEmpty(tokenResponse?.access_token))
                    {
                        return Result<KiotVietTokenResponseDto>.Failure("SYNC_KIOTVIET_INVALID_TOKEN_RESPONSE");
                    }

                    return Result<KiotVietTokenResponseDto>.Success(tokenResponse);
                }
                else
                {
                    // Log chi tiết lỗi để debug
                    var errorDetail = $"HTTP {response.StatusCode}: {responseContent}";
                    return Result<KiotVietTokenResponseDto>.Failure($"SYNC_KIOTVIET_TOKEN_REQUEST_FAILED: {errorDetail}");
                }
            }
            catch (HttpRequestException httpEx)
            {
                return Result<KiotVietTokenResponseDto>.Failure($"SYNC_KIOTVIET_HTTP_ERROR: {httpEx.Message}");
            }
            catch (JsonException jsonEx)
            {
                return Result<KiotVietTokenResponseDto>.Failure($"SYNC_KIOTVIET_JSON_PARSE_ERROR: {jsonEx.Message}");
            }
            catch (Exception ex)
            {
                return Result<KiotVietTokenResponseDto>.Failure($"SYNC_KIOTVIET_TOKEN_EXCEPTION: {ex.Message}");
            }
        }

        public async Task<SyncServiceConfig> GetKiotVietConfig(string shopId)
        {
            var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.KiotViet);
            return config;
        }

        public async Task<Result<bool>> DeleteKiotVietConfig(string shopId)
        {
            try
            {
                var config = await GetKiotVietConfig(shopId);
                if (config == null) return Result<bool>.Failure("SYNC_KIOTVIET_CONFIG_NOT_FOUND");

                var deleted = await _syncConfigRepository.DeleteByShopIdAndService(shopId, SyncServiceEnum.KiotViet);
                return Result<bool>.Success(true);
            }
            catch (Exception ex)
            {
                return Result<bool>.Failure("SYNC_KIOTVIET_CONFIG_DELETE_ERROR");
            }
        }

        public async Task<Result<SyncServiceConfig>> UpdateKiotVietAccessCode(string shopId, string accessCode)
        {
            var config = await GetKiotVietConfig(shopId);
            if (config == null)
                return Result<SyncServiceConfig>.Failure("SYNC_KIOTVIET_CONFIG_NOT_FOUND");

            try
            {
                config.SecretKey = _cryptoRepository.Decrypt(config.SecretKey);

                // Lấy access token từ KiotViet API
                var tokenResult = await GetAccessToken(config.AppId, config.SecretKey, accessCode);
                if (!tokenResult.IsSuccess)
                    return Result<SyncServiceConfig>.Failure("SYNC_KIOTVIET_GET_TOKEN_ERROR");

                // Cập nhật access token và access code
                await _syncConfigRepository.UpdateAccessCode(shopId, SyncServiceEnum.KiotViet, accessCode);
                await _syncConfigRepository.UpdateAccessToken(shopId, SyncServiceEnum.KiotViet, tokenResult.Data);

                // Tạo verify token nếu chưa có
                if (string.IsNullOrEmpty(config.VerifyToken))
                {
                    var verifyToken = TokenUtil.GenerateRandomVerifyToken();
                    await _syncConfigRepository.UpdateVerifyToken(shopId, SyncServiceEnum.KiotViet, verifyToken);
                }

                // Cập nhật trạng thái config thành actived
                await _syncConfigRepository.UpdateStatus(shopId, SyncServiceEnum.KiotViet, TypeStatus.Actived);

                return Result<SyncServiceConfig>.Success(config);
            }
            catch (Exception ex)
            {
                return Result<SyncServiceConfig>.Failure("SYNC_KIOTVIET_UPDATE_ACCESS_CODE_ERROR");
            }
        }

        /// <summary>
        /// Lấy access token từ KiotViet API (phiên bản public)
        /// </summary>
        public async Task<Result<string>> GetAccessToken(string clientId, string clientSecret, string domain)
        {
            try
            {
                using var httpClient = new HttpClient();

                // Sử dụng domain được cung cấp để tạo token URL
                var tokenUrl = $"{domain}/oauth2/token";

                // Theo tài liệu KiotViet API - sử dụng application/x-www-form-urlencoded
                var requestParams = new List<KeyValuePair<string, string>>
                {
                    new("scopes", "PublicApi.Access"),
                    new("grant_type", "client_credentials"),
                    new("client_id", clientId),
                    new("client_secret", clientSecret)
                };

                var content = new FormUrlEncodedContent(requestParams);

                var response = await httpClient.PostAsync(tokenUrl, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var tokenResponse = JsonConvert.DeserializeObject<KiotVietTokenResponseDto>(responseContent);
                    return Result<string>.Success(tokenResponse.access_token);
                }

                return Result<string>.Failure($"SYNC_KIOTVIET_GET_TOKEN_ERROR: {responseContent}");
            }
            catch (Exception ex)
            {
                return Result<string>.Failure($"SYNC_KIOTVIET_GET_TOKEN_EXCEPTION: {ex.Message}");
            }
        }

        public async Task<Result<bool>> SyncKiotVietProductFromWebhook(object productData, string shopId)
        {
            try
            {
                var kiotVietProduct = JsonConvert.DeserializeObject<KiotVietProductWebhookDto>(productData.ToString());
                var shop = _shopRepository.FindByShopId(shopId);
                if (shop == null) return Result<bool>.Failure("SHOP_NOT_FOUND");

                // Lấy category từ KiotViet nếu cần
                var categoryId = kiotVietProduct.CategoryId?.ToString();
                var category = await EnsureCategoryExists(shopId, categoryId, kiotVietProduct.CategoryName);

                // Ánh xạ dữ liệu sản phẩm
                var productDto = MapKiotVietProductToProductDto(kiotVietProduct, shopId, category?.CategoryId);

                // Tạo ItemsCode duy nhất
                string itemsCode = $"KIOTVIET_{kiotVietProduct.Id}";

                // Kiểm tra sản phẩm đã tồn tại
                var existingGroupItems = _itemsRepository.GroupByVariant(itemsCode);

                if (existingGroupItems != null)
                {
                    // Update existing product
                    await UpdateExistingProductGroup(existingGroupItems, productDto, itemsCode);
                }
                else
                {
                    // Create new product
                    await CreateNewProductGroup(productDto, itemsCode, kiotVietProduct.Id.ToString());
                }

                return Result<bool>.Success(true);
            }
            catch (Exception ex)
            {
                return Result<bool>.Failure($"SYNC_KIOTVIET_PRODUCT_ERROR: {ex.Message}");
            }
        }

        public async Task<Result<bool>> SyncKiotVietOrderFromWebhook(object orderData, string shopId)
        {
            try
            {
                var kiotVietOrder = JsonConvert.DeserializeObject<KiotVietOrderWebhookDto>(orderData.ToString());


                var existingOrder = _syncServiceHelper.FindOrderByExternalId(shopId, kiotVietOrder.Id.ToString(), SyncServiceEnum.KiotViet);

                // Lấy thông tin customer
                var customerResult = await GetKiotVietCustomerByIdAsync(shopId, kiotVietOrder.CustomerId);

                var user = EnsureUserFromKiotVietCustomer(customerResult.Data, shopId);

                // Map order từ KiotViet
                var mappedOrder = MapKiotVietOrderToOrder(kiotVietOrder, shopId, user);
                if (mappedOrder == null)
                    return Result<bool>.Failure("SYNC_KIOTVIET_ORDER_MAPPING_ERROR");

                if (existingOrder != null)
                {
                    // Update existing order
                    UpdateExistingOrder(existingOrder, mappedOrder);
                    _orderRepository.UpdateOrder(existingOrder);
                }
                else
                {
                    // Create new order
                    await _orderRepository.CreateOrder(mappedOrder);
                }

                return Result<bool>.Success(true);
            }
            catch (Exception ex)
            {
                return Result<bool>.Failure($"SYNC_KIOTVIET_ORDER_ERROR: {ex.Message}");
            }
        }

        public async Task<Result<bool>> SyncKiotVietCustomerFromWebhook(object customerData, string shopId)
        {
            try
            {
                var kiotVietCustomer = JsonConvert.DeserializeObject<KiotVietCustomerWebhookDto>(customerData.ToString());
                var user = EnsureUserFromKiotVietCustomer(kiotVietCustomer, shopId);
                return Result<bool>.Success(true);
            }
            catch (Exception ex)
            {
                return Result<bool>.Failure($"SYNC_KIOTVIET_CUSTOMER_ERROR: {ex.Message}");
            }
        }

        public async Task<Result<bool>> DeleteKiotVietProductsFromWebhook(object productIds)
        {
            try
            {
                if (productIds is List<int> kiotVietProductIds)
                {
                    foreach (var productId in kiotVietProductIds)
                    {
                        var existingItem = _syncServiceHelper.FindItemByExternalId("", productId.ToString(), SyncServiceEnum.KiotViet);
                        if (existingItem != null)
                        {
                            _itemsRepository.DeleteItems(existingItem.ItemsId);
                        }
                    }
                }
                return Result<bool>.Success(true);
            }
            catch (Exception ex)
            {
                return Result<bool>.Failure($"SYNC_KIOTVIET_DELETE_PRODUCT_ERROR: {ex.Message}");
            }
        }

        public async Task<Result<bool>> DeleteKiotVietOrderFromWebhook(object orderData, string shopId)
        {
            try
            {
                var kiotVietOrder = JsonConvert.DeserializeObject<KiotVietOrderWebhookDto>(orderData.ToString());
                var existingOrder = _syncServiceHelper.FindOrderByExternalId(shopId, kiotVietOrder.Id.ToString(), SyncServiceEnum.KiotViet);

                if (existingOrder != null)
                {
                    _orderRepository.DeleteOrder(existingOrder.OrderId);
                }

                return Result<bool>.Success(true);
            }
            catch (Exception ex)
            {
                return Result<bool>.Failure($"SYNC_KIOTVIET_DELETE_ORDER_ERROR: {ex.Message}");
            }
        }

        public async Task<Result<bool>> UpdateOrderToKiotViet(Order order, string shopId)
        {
            var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.KiotViet);
            if (config == null || string.IsNullOrEmpty(config.AccessToken))
                return Result<bool>.Failure("SYNC_KIOTVIET_SHOP_NOT_CONFIGURED");

            try
            {
                config.SecretKey = _cryptoRepository.Decrypt(config.SecretKey);

                // Tạo order data để gửi lên KiotViet
                var orderData = new
                {
                    Code = order.OrderNo,
                    CustomerId = order.UserShippingAddress?.UserId,
                    OrderDetails = order.ListItems?.Select(x => new
                    {
                        ProductId = x.ExternalId,
                        Quantity = x.Quantity,
                        Price = x.Price
                    }).ToList(),
                    Description = order.Notes,
                    Total = order.TotalAfterTax
                };

                using var httpClient = new HttpClient();
                httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {config.AccessToken}");

                var json = JsonConvert.SerializeObject(orderData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await httpClient.PostAsync($"{config.AdditionalConfig}/orders", content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var createOrderResponse = JsonConvert.DeserializeObject<KiotVietCreateOrderResponseDto>(responseContent);

                    // Update order với external info
                    order.ExternalSource = SyncServiceEnum.KiotViet;
                    order.ExternalId = createOrderResponse.Id.ToString();
                    _orderRepository.UpdateOrder(order);

                    return Result<bool>.Success(true);
                }

                return Result<bool>.Failure("SYNC_KIOTVIET_UPDATE_ORDER_ERROR");
            }
            catch (Exception ex)
            {
                return Result<bool>.Failure($"SYNC_KIOTVIET_UPDATE_ORDER_EXCEPTION: {ex.Message}");
            }
        }
        /// <summary>
        /// Đăng ký webhooks với KiotViet API
        /// </summary>
        private async Task<Result<bool>> RegisterKiotVietWebhooks(string accessToken, string shopId, string retailerId)
        {
            try
            {
                string url = "https://public.kiotapi.com/webhooks";

                using var httpClient = new HttpClient();
                httpClient.DefaultRequestHeaders.Clear();
                httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue(
                    "Bearer",
                    accessToken
                );
                httpClient.DefaultRequestHeaders.Add("Retailer", retailerId);

                var webhookTypes = new[]
                {
            "product.update",
            "product.delete",
            "stock.update",
            "order.update",
            "invoice.update",
            "invoice.delete",
            "customer.update",
            "customer.delete",
            "category.update",
            "category.delete"
        };

                int count = webhookTypes.Length;
                long[] webhookIds = new long[count];

                for (int i = 0; i < count; i++)
                {
                    var body = new
                    {
                        Webhook = new
                        {
                            Type = $"{webhookTypes[i]}",
                            Url = GetDefaultWebhookUrl(),
                            IsActive = true,
                            Description = $"{webhookTypes[i]}"
                        }
                    };

                    var jsonBody = JsonConvert.SerializeObject(body);
                    var data = new StringContent(jsonBody, Encoding.UTF8, "application/json");
                    var response = await httpClient.PostAsync(url, data);

                    if (response.IsSuccessStatusCode)
                    {
                        var resBody = await response.Content.ReadAsStringAsync();
                        var json = System.Text.Json.JsonDocument.Parse(resBody);
                        webhookIds[i] = json.RootElement.GetProperty("id").GetInt64();
                    }
                    else
                    {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        return Result<bool>.Failure($"Failed to register webhook {webhookTypes[i]}: {errorContent}");
                    }
                }

                return Result<bool>.Success(true, $"Successfully registered {count} webhooks");
            }
            catch (Exception ex)
            {
                return Result<bool>.Failure($"SYNC_KIOTVIET_WEBHOOK_REGISTRATION_ERROR: {ex.Message}");
            }
        }

        /// <summary>
        /// Tạo secret key ngẫu nhiên cho webhook
        /// </summary>
        private string GenerateWebhookSecret()
        {
            using (var rng = new System.Security.Cryptography.RNGCryptoServiceProvider())
            {
                var bytes = new byte[32]; // 256 bits
                rng.GetBytes(bytes);
                return Convert.ToBase64String(bytes);
            }
        }

        /// <summary>
        /// Lưu webhook secret vào database
        /// </summary>
        private async Task SaveWebhookSecret(string shopId, string webhookSecret)
        {
            try
            {
                // Mã hóa secret trước khi lưu
                var encryptedSecret = _cryptoRepository.Encrypt(webhookSecret);

                // Lưu vào field WebhookSecret của SyncServiceConfig
                await _syncConfigRepository.UpdateWebhookSecret(shopId, SyncServiceEnum.KiotViet, encryptedSecret);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to save webhook secret: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy webhook secret từ database
        /// </summary>
        public async Task<string> GetWebhookSecret(string shopId)
        {
            try
            {
                var config = await GetKiotVietConfig(shopId);
                if (config == null || string.IsNullOrEmpty(config.WebhookSecret))
                    return null;

                // Giải mã secret
                return _cryptoRepository.Decrypt(config.WebhookSecret);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to get webhook secret: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Verify webhook signature từ KiotViet
        /// </summary>
        public bool VerifyWebhookSignature(string payload, string signature, string secret)
        {
            try
            {
                if (string.IsNullOrEmpty(payload) || string.IsNullOrEmpty(signature) || string.IsNullOrEmpty(secret))
                    return false;

                // KiotViet sử dụng HMAC-SHA256 để ký webhook
                using (var hmac = new System.Security.Cryptography.HMACSHA256(Encoding.UTF8.GetBytes(secret)))
                {
                    var computedHash = hmac.ComputeHash(Encoding.UTF8.GetBytes(payload));
                    var computedSignature = Convert.ToBase64String(computedHash);

                    // So sánh signature (case-sensitive)
                    return signature.Equals(computedSignature, StringComparison.Ordinal);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to verify webhook signature: {ex.Message}");
                return false;
            }
        }




        /// <summary>
        /// Xử lý webhook cập nhật tồn kho
        /// </summary>
        public async Task<Result<bool>> SyncKiotVietStockFromWebhook(object stockData, string shopId)
        {
            try
            {
                var kiotVietStock = JsonConvert.DeserializeObject<KiotVietStockWebhookDto>(stockData.ToString());

                // Tìm sản phẩm theo external ID
                var existingItem = _syncServiceHelper.FindItemByExternalId(shopId, kiotVietStock.ProductId.ToString(), SyncServiceEnum.KiotViet);
                if (existingItem != null)
                {
                    // Cập nhật tồn kho
                    existingItem.Quantity = kiotVietStock.OnHand;
                    existingItem.Updated = DateTime.UtcNow;
                    _itemsRepository.UpdateItems(existingItem);
                }

                return Result<bool>.Success(true);
            }
            catch (Exception ex)
            {
                return Result<bool>.Failure($"SYNC_KIOTVIET_STOCK_ERROR: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý webhook cập nhật category
        /// </summary>
        public async Task<Result<bool>> SyncKiotVietCategoryFromWebhook(object categoryData, string shopId)
        {
            try
            {
                var kiotVietCategory = JsonConvert.DeserializeObject<KiotVietCategoryWebhookDto>(categoryData.ToString());

                var existingCategory = await _categoryRepository.FindByExternalId(shopId, kiotVietCategory.Id.ToString(), SyncServiceEnum.KiotViet);
                if (existingCategory != null)
                {
                    // Cập nhật category
                    existingCategory.CategoryName = kiotVietCategory.Name;
                    existingCategory.Updated = DateTime.UtcNow;
                    await _categoryRepository.UpdateCategory(existingCategory);
                }
                else
                {
                    // Tạo category mới
                    await EnsureCategoryExists(shopId, kiotVietCategory.Id.ToString(), kiotVietCategory.Name);
                }

                return Result<bool>.Success(true);
            }
            catch (Exception ex)
            {
                return Result<bool>.Failure($"SYNC_KIOTVIET_CATEGORY_ERROR: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý webhook xóa category
        /// </summary>
        public async Task<Result<bool>> DeleteKiotVietCategoryFromWebhook(object categoryData, string shopId)
        {
            try
            {
                var kiotVietCategory = JsonConvert.DeserializeObject<KiotVietCategoryWebhookDto>(categoryData.ToString());

                var existingCategory = await _categoryRepository.FindByExternalId(shopId, kiotVietCategory.Id.ToString(), SyncServiceEnum.KiotViet);
                if (existingCategory != null)
                {
                    await _categoryRepository.DeleteCategory(existingCategory.CategoryId);
                }

                return Result<bool>.Success(true);
            }
            catch (Exception ex)
            {
                return Result<bool>.Failure($"DELETE_KIOTVIET_CATEGORY_ERROR: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý webhook xóa customer
        /// </summary>
        public async Task<Result<bool>> DeleteKiotVietCustomerFromWebhook(object customerData, string shopId)
        {
            try
            {
                var kiotVietCustomer = JsonConvert.DeserializeObject<KiotVietCustomerWebhookDto>(customerData.ToString());

                // Tìm user theo external ID hoặc phone
                var existingUser = _userRepository.FindByUserPhone(shopId, kiotVietCustomer.Id.ToString());
                if (existingUser != null)
                {
                    // Soft delete user
                    existingUser.Status = TypeStatus.InActived;
                    existingUser.Updated = DateTime.UtcNow;
                    _userRepository.UpdateUser(existingUser);
                }

                return Result<bool>.Success(true);
            }
            catch (Exception ex)
            {
                return Result<bool>.Failure($"DELETE_KIOTVIET_CUSTOMER_ERROR: {ex.Message}");
            }
        }
        #region Private Helper Methods

        private string GetDefaultWebhookUrl()
        {
            string domainWildcard = "https://sdhpdd07-5077.asse.devtunnels.ms";
            //string domainWildcard = "https://dev-admin.evotech.vn";
            //string domainWildcard = _configuration["ApplicationSettings:DomainWildcard"] ?? @"localhost";
            return domainWildcard + "/api/webhook/KiotViet";
        }
    

        private async Task<Result<KiotVietCustomerDto>> GetKiotVietCustomerByIdAsync(string shopId, int customerId)
        {
            var config = await _syncConfigRepository.FindByShopIdAndService(shopId, SyncServiceEnum.KiotViet);
            if (config == null || string.IsNullOrEmpty(config.AccessToken))
                return Result<KiotVietCustomerDto>.Failure("SYNC_KIOTVIET_CONFIG_NOT_FOUND");

            try
            {
                using var httpClient = new HttpClient();
                httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {config.AccessToken}");
                httpClient.DefaultRequestHeaders.Add("Retailer", config.AdditionalConfig);

                var response = await httpClient.GetAsync($"https://public.kiotapi.com/customers/{customerId}");
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var customerDto = JsonConvert.DeserializeObject<KiotVietCustomerDto>(responseContent);
                    return Result<KiotVietCustomerDto>.Success(customerDto);
                }

                return Result<KiotVietCustomerDto>.Failure("SYNC_KIOTVIET_GET_CUSTOMER_ERROR");
            }
            catch (Exception ex)
            {
                return Result<KiotVietCustomerDto>.Failure($"SYNC_KIOTVIET_GET_CUSTOMER_EXCEPTION: {ex.Message}");
            }
        }

        private User EnsureUserFromKiotVietCustomer(dynamic kiotVietCustomer, string shopId)
        {
            User? user = null;

            string phone = kiotVietCustomer?.ContactNumber?.ToString() ?? "";
            string email = kiotVietCustomer?.Email?.ToString() ?? "";
            string name = kiotVietCustomer?.Name?.ToString() ?? "";
            string address = kiotVietCustomer?.Address?.ToString() ?? "";
            string ward = kiotVietCustomer?.WardName?.ToString() ?? "";


            if (!string.IsNullOrEmpty(phone))
                user = _userRepository.FindByUsername(shopId, name);

            if (user == null)
            {
                user = new User
                {
                    UserId = Guid.NewGuid().ToString(),
                    ShopId = shopId,
                    Email = email,
                    PartnerId = kiotVietCustomer?.Id?.ToString(),
                    PhoneNumber = phone,
                    Fullname = name,
                    Address = address,
                    WardName =ward,
                    
                    Status = TypeStatus.Actived,
                    Created = DateTime.Now,
                };
                _userRepository.CreateUser(user);
            }
            else
            {
                user.Email = email;
                user.Fullname = name;
                user.PhoneNumber = phone;
                user.Address = address;
                user.WardName = ward;
                user.Updated = DateTime.Now;
                _userRepository.UpdateUser(user);
            }

            return user;
        }

        private async Task<Category> EnsureCategoryExists(string shopId, string categoryId, string categoryName)
        {
            if (string.IsNullOrEmpty(categoryId)) return null;

            var existingCategory = await _categoryRepository.FindByExternalId(shopId, categoryId, SyncServiceEnum.KiotViet);
            if (existingCategory != null) return existingCategory;

            var shop = _shopRepository.FindByShopId(shopId);
            if (shop == null) return null;

            var newCategory = new Category
            {
                CategoryId = Guid.NewGuid().ToString(),
                PartnerId = shop.PartnerId,
                CategoryName = categoryName ?? $"KiotViet Category {categoryId}",
                ShopId = shopId,
                CategoryType = TypeCategory.Product,
                CategoryLevel = "1",
                Publish = TypeCategoryPublish.Publish,
                Active = TypeRuleActive.Actived,
                ExternalSource = SyncServiceEnum.KiotViet,
                ExternalId = categoryId,
                Created = DateTime.UtcNow,
                Updated = DateTime.UtcNow
            };

            return await _categoryRepository.CreateCategory(newCategory);
        }

        private ProductDto MapKiotVietProductToProductDto(KiotVietProductWebhookDto kiotVietProduct, string shopId, string categoryId)
        {
            var shop = _shopRepository.FindByShopId(shopId);
            if (shop == null) return null;

            var warehouse = _warehouseRepository.ListWarehouse(new Paging { PageSize = 1, PageIndex = 0 }, shop.PartnerId, shopId)
                .Result?.FirstOrDefault();

            var imageList = new List<MediaInfo>();
            if (!string.IsNullOrEmpty(kiotVietProduct.Images))
            {
                imageList.Add(new MediaInfo
                {
                    MediaFileId = Guid.NewGuid().ToString(),
                    Type = TypeMedia.IMAGE,
                    Link = kiotVietProduct.Images
                });
            }

            return new ProductDto
            {
                PartnerId = shop?.PartnerId,
                ItemsName = kiotVietProduct.Name,
                ItemsCode = kiotVietProduct.Code,
                ExternalSource = SyncServiceEnum.KiotViet,
                CategoryIds = !string.IsNullOrEmpty(categoryId) ? [categoryId] : [],
                Price = (long?)kiotVietProduct.BasePrice,
                PriceReal = (long?)kiotVietProduct.BasePrice,
                PriceCapital = (long?)kiotVietProduct.BasePrice,
                Quantity = 0, // KiotViet webhook không có thông tin inventory
                ItemsInfo = kiotVietProduct.Description,
                QuantityPurchase = 1,
                Images = imageList,
                ItemsType = TypeItems.Product,
                ShopId = shopId,
                IsTop = false,
                IsShow = kiotVietProduct.IsActive,
                WarehouseId = warehouse?.WarehouseId,
                IsVariant = false,
                Created = DateTime.UtcNow,
                ListVariant = []
            };
        }

        private Order MapKiotVietOrderToOrder(KiotVietOrderWebhookDto kiotVietOrder, string shopId, User user)
        {
            var shop = _shopRepository.FindByShopId(shopId);
            if (shop == null) return null;

            var listItems = new List<ItemsOrder>();
            foreach (var detail in kiotVietOrder.OrderDetails ?? new List<KiotVietOrderDetailDto>())
            {
                
                    var itemsOrder = new ItemsOrder();
                    itemsOrder.Quantity = detail.Quantity;
                    itemsOrder.Price = (long?)detail.Price;
                    itemsOrder.ItemsCode = detail.ProductCode;
                    itemsOrder.ItemsName =detail.ProductName;
                    itemsOrder.ExternalId = detail.ProductId?.ToString();
                    itemsOrder.ExternalSource = SyncServiceEnum.KiotViet;
                    itemsOrder.ShopId = shopId;
                    itemsOrder.VoucherDiscount = detail.Discount;
                    listItems.Add(itemsOrder);
                
            }

            var shippingAddress = new ShippingAddress
            {
                UserId = user.UserId,
                FullName = user.Fullname,
                PhoneNumber = user.PhoneNumber,
                Address = user.Address,
            };

            var totalPrice = listItems.Sum(item => (item.Price ?? 0) * (item.Quantity ?? 0));

            return new Order
            {
                ShopId = shopId,
                PartnerId = shop.PartnerId,
                TransactionId = Guid.NewGuid().ToString(),
                ListItems = listItems,
                Price = totalPrice,
                Notes = kiotVietOrder.Description,
                OrderOrigin = TypeOrigin.KiotViet,
                StatusOrder = MapKiotVietOrderStatus(kiotVietOrder.Status),
                StatusTransport = MapKiotVietTransportStatus(kiotVietOrder.Status),
                TypePay = TypePayment.COD,
                StatusPay = TypePayStatus.NotPaid,
                TransportService = TypeTransportService.LCOD,
                TransportPrice = 0,
                StatusDelivery = TypeDelivery.ExpressDelivery,
                TotalTaxAmount = 0,
                TotalAfterTax = kiotVietOrder.Total,
                UserShippingAddress = shippingAddress,
                OrderNo = kiotVietOrder.Code,
                Creator = shippingAddress,
                Created = kiotVietOrder.CreatedDate,
                Status = TypeStatus.Actived,
                
                ExternalSource = SyncServiceEnum.KiotViet,
                ExternalId = kiotVietOrder.Id.ToString(),
            };
        }

        private TypeOrderStatus MapKiotVietOrderStatus(string status)
        {
            return status?.ToLower() switch
            {
                "new" or "pending" => TypeOrderStatus.Pending,
                "confirmed" => TypeOrderStatus.Verified,
                "delivered" => TypeOrderStatus.Success,
                "cancelled" => TypeOrderStatus.Failed,
                _ => TypeOrderStatus.Pending
            };
        }

        private TypeTransportStatus MapKiotVietTransportStatus(string status)
        {
            return status?.ToLower() switch
            {
                "new" or "pending" => TypeTransportStatus.Created,
                "confirmed" => TypeTransportStatus.WaitingForDelivery,
                "delivering" => TypeTransportStatus.Delivering,
                "delivered" => TypeTransportStatus.Success,
                "cancelled" => TypeTransportStatus.Cancel,
                _ => TypeTransportStatus.Created
            };
        }

        private Task CreateNewProductGroup(ProductDto productDto, string itemsCode, string externalId)
        {
            var items = _mapper.Map<Items>(productDto);
            items.ItemsId = Guid.NewGuid().ToString();
            items.ItemsCode = itemsCode;
            items.ItemsType = TypeItems.Product;
            items.Status = TypeStatus.Actived;
            items.Created = DateTime.Now;
            items.ExternalSource = SyncServiceEnum.KiotViet;
            items.ExternalId = externalId;
            _itemsRepository.CreateItems(items);

            return Task.CompletedTask;
        }

        private Task UpdateExistingProductGroup(ItemsGroupBy existingGroupItems, ProductDto productDto, string itemsCode)
        {
            var existingItem = _itemsRepository.FindByItemsId(existingGroupItems.ItemsId);
            if (existingItem != null)
            {
                _itemsFlow.UpdateProductFields(existingItem, productDto);
                existingItem.Updated = DateTime.UtcNow;
                _itemsRepository.UpdateItems(existingItem);
            }

            return Task.CompletedTask;
        }

        private void UpdateExistingOrder(Order existingOrder, Order mappedOrder)
        {
            existingOrder.Notes = mappedOrder.Notes;
            existingOrder.StatusOrder = mappedOrder.StatusOrder;
            existingOrder.StatusTransport = mappedOrder.StatusTransport;
            existingOrder.TotalAfterTax = mappedOrder.TotalAfterTax;
            existingOrder.Price = mappedOrder.Price;
            existingOrder.UserShippingAddress = mappedOrder.UserShippingAddress;
            existingOrder.ListItems = mappedOrder.ListItems;
            existingOrder.Updated = DateTime.Now;
        }

        #endregion
    }
}
